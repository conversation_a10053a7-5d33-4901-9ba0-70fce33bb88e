from fastapi import FastAP<PERSON>, Request, Form, File, UploadFile
from fastapi.responses import JSONResponse
import uvicorn
import ollama
import faiss
import pickle
import numpy as np
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from langchain_ollama import OllamaEmbeddings
import json
import os
import re
import sys
import traceback
import logging
from datetime import datetime, timedelta
import statistics
from dateutil import parser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('agent_server.log', encoding='utf-8')
    ]
)

# Add tools path to system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))

# Import tools
try:
    from tools.tools_health_score import HealthScoreAnalysisTool, health_score_analysis_tool
    from tools.tools_monitor_vital_signs import monitor_vital_signs
    from tools.tools_health_data_json import get_default_health_data
    from tools.tools_kidney_function import kidney_function_analysis_tool
    from tools.tools_lipid_profile import analyze_lipid_profile
    from tools.tools_health_consult import automated_health_consultation_tool
    from tools.tools_lung_capacity import lung_capacity_analyzer_tool
    from tools.tools_test_results import interpret_test_results
    from tools.tools_device_recommender import device_recommender_tool, device_confirmation_tool, recommend_health_devices, confirm_device_recommendation
    from tools.tools_symptom_checker import symptom_checker_tool, analyze_symptoms
    from tools.tools_chronic_tracker import chronic_tracker_tool
    from tools.tools_lab_test_explainer import explain_lab_test, list_available_tests
    from tools.tools_followup_reminder import followup_reminder_tool
    from tools.tools_chronic_risk import predict_chronic_risk
    from tools.tools_doc_summarizer import summarize_medical_text, extract_text_from_upload
    from tools.tools_lifestyle_coach import (record_habits, compute_weekly_habit_summary, generate_lifestyle_recommendations)
    from tools.tools_weekly_digest import generate_weekly_digest
    from tools.tools_progress_tracker import (generate_monthly_summary, generate_trend_recommendations)
    from tools.tools_mental_health_assessment import MentalHealthAssessmentTool
    from tools.tools_liver_function import (analyze_liver_function, extract_lft_values, MedicalConditionEnum, SmokingAlcoholEnum, DietaryHabitsEnum, MedicationsEnum, SymptomEnum, HepatitisMarkerEnum, ManualEntryRequest)


    logging.info("Successfully imported health tools")
except ImportError as e:
    logging.error(f"Failed to import tools: {e}")
    sys.exit(1)

# Initialize FastAPI app
app = FastAPI(title="Integrated Health Agent API",
              description="API that combines chat, health score analysis, vital signs monitoring, and health consultation")

# Add global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    error_msg = f"Unhandled error: {str(exc)}"
    logging.error(f"Exception: {error_msg}")
    logging.error(f"Traceback: {traceback.format_exc()}")
    # Use request parameter to get the path that caused the error
    path = request.url.path if hasattr(request, 'url') else "unknown path"
    logging.error(f"Error occurred at path: {path}")
    return JSONResponse(
        status_code=500,
        content={"error": error_msg}
    )

# === MODEL CONSTANTS ===
QWEN_MODEL = "qwen2.5:1.5b"
DEEPSEEK_MODEL = "deepseek-r1:1.5b"
DEFAULT_MODEL = QWEN_MODEL

# === VECTOR STORE PATHS ===
VECTOR_STORE_PATHS = {
    QWEN_MODEL: {
        "index": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\qwen2.5-1.5b\index.faiss",
        "metadata": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\qwen2.5-1.5b\index.pkl"
    },
    DEEPSEEK_MODEL: {
        "index": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\deepseek-r1-1.5b\index.faiss",
        "metadata": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\deepseek-r1-1.5b\index.pkl"
    }
}

# === STORE CHAT TITLES, HISTORIES, AND USER HEALTH DATA ===
chat_titles: Dict[tuple, str] = {}  # Dictionary to store session titles per user (patient_id, session_id)
chat_histories: Dict[tuple, List[Dict[str, str]]] = {}  # Dictionary to store chat histories by (patient_id, session_id)
user_health_data: Dict[tuple, Dict[str, Any]] = {}  # Dictionary to store health data by (patient_id, session_id)
MAX_HISTORY_LENGTH = 10
TOP_K = 1  # Number of relevant documents to fetch

# === LOAD FAISS INDEXES & METADATA ===
vector_indexes = {}
vector_docs = {}
embedding_models = {}

for model_name, paths in VECTOR_STORE_PATHS.items():
    try:
        vector_indexes[model_name] = faiss.read_index(paths["index"])
        logging.info(f"✅ FAISS index loaded for {model_name}")
    except Exception as e:
        logging.error(f"❌ Error loading FAISS index for {model_name}: {e}")

    try:
        with open(paths["metadata"], "rb") as f:
            vector_docs[model_name] = pickle.load(f)
        logging.info(f"✅ Metadata loaded for {model_name}")
    except Exception as e:
        logging.error(f"❌ Error loading metadata for {model_name}: {e}")

    try:
        embedding_models[model_name] = OllamaEmbeddings(model=model_name)
        logging.info(f"✅ Embedding model loaded for {model_name}")
    except Exception as e:
        logging.error(f"❌ Error loading embedding model {model_name}: {e}")

# === REQUEST MODELS ===
class ChatRequest(BaseModel):
    session_id: str
    user_id: str
    query: str
    model: str = DEFAULT_MODEL

class VitalSignsRequest(BaseModel):
    user_id: str
    vital_signs: Dict[str, Any]

class HealthScoreRequest(BaseModel):
    user_id: str
    health_data: Dict[str, Any]  # Allow any type of value (string, float, etc.)

class KidneyFunctionRequest(BaseModel):
    user_id: str
    kidney_data: Dict[str, Any]  # Accepts both float and string types like "Sex"

class LipidProfileRequest(BaseModel):
    user_id: str
    lipid_data: Dict[str, Any]  # Accepts both string and numeric values

class LungCapacityRequest(BaseModel):
    user_id: str
    spirometry_data: Dict[str, Any]  # Accepts both numeric values and patient information

class UserIDRequest(BaseModel):
    user_id: str

class TestResultsRequest(BaseModel):
    user_id: str
    test_results: Dict[str, Any]  # Contains test results and patient information

class RealTimeHealthScoreRequest(BaseModel):
    user_id: str
    health_data: Dict[str, Any]  # Contains vitals, lifestyle factors, and test results

class DeviceRecommendationRequest(BaseModel):
    user_id: str
    health_data: Optional[Dict[str, Any]] = None  # Optional health data, will use stored data if not provided
    test_type: Optional[str] = None  # Type of test (vital_signs, kidney_function, lipid_profile, etc.)
    test_data: Optional[Dict[str, Any]] = None  # Test-specific data

class DeviceConfirmationRequest(BaseModel):
    user_id: str
    confirmed: bool  # Whether user confirmed they want device recommendations

class SymptomCheckerRequest(BaseModel):
    user_id: str
    symptoms: str
    age: Optional[int] = None
    sex: Optional[str] = None
    duration: Optional[str] = None
    severity: Optional[str] = "moderate"

class ChronicTrackerRequest(BaseModel):
    user_id: str
    condition_data: Dict[str, Any]  # Contains chronic condition data and tracking information
    tracking_frequency: Optional[str] = "as_needed"  # Frequency of tracking: "daily", "weekly", "monthly", "as_needed"
    measurement_date: Optional[str] = None  # Date/time of measurement (ISO format)

class LabTestExplainerRequest(BaseModel):
    user_id: str
    test_name: Optional[str] = None
    list_tests: bool = False

class FollowupReminderRequest(BaseModel):
    user_id: str
    health_data: Optional[Dict[str, Any]] = None  # Optional health data, will use stored data if not provided
    test_type: Optional[str] = None  # Type of test (vital_signs, kidney_function, lipid_profile, etc.)

class ProgressTrackRequest(BaseModel):
    user_id: str
    vital_signs: Dict[str, Any]

class ChronicRiskRequest(BaseModel):
    user_id: str
    chronic_data: Dict[str, Any]

class UserProfileRequest(BaseModel):
    user_id: str
    profile: Dict[str, Any]

class DocSummaryRequest(BaseModel):
    user_id: str
    model: Optional[str] = DEFAULT_MODEL  # Optional fallback

class LifestyleHabitRequest(BaseModel):
    user_id: str
    habits: Dict[str, float]  # e.g., {"walk": 5000, "water": 6, ...}

class DigestRequest(BaseModel):
    user_id: str
    vital_signs: Dict[str, Any]

class MentalHealthAssessmentRequest(BaseModel):
    user_id: str
    assessment_data: Dict[str, Any]  # Contains age, gender, country, stress responses, PHQ-9, GAD-7, etc.


class LiverFunctionAssessmentRequest(BaseModel):
    user_id: str
    lft_data: ManualEntryRequest

# === HELPER FUNCTIONS ===
def generate_chat_title(first_query: str) -> str:
    """Generate a title by extracting key words from the query."""
    words = first_query.split()[:10]  # Take the first 5 words
    return " ".join(words).title()

def retrieve_context(query: str, model_name: str, top_k: int = TOP_K):
    """Retrieve relevant context from the vector store"""
    if model_name not in vector_indexes or model_name not in vector_docs:
        logging.warning(f"Vector index or docs not found for model {model_name}")
        return ""

    try:
        # Get the embedding model
        embedder = embedding_models[model_name]

        # Generate embedding for the query
        query_embedding = np.array([embedder.embed_query(query)]).astype("float32")

        # Search the vector index
        index = vector_indexes[model_name]
        documents = vector_docs[model_name]

        _, indices = index.search(query_embedding, top_k)

        # Get the relevant documents
        relevant_docs = [
            documents.get(int(idx), {}).get("text", "") if isinstance(documents.get(int(idx), {}), dict)
            else str(documents.get(int(idx), ""))
            for idx in indices[0]
        ]


        return " ".join(relevant_docs)
    except Exception as e:
        logging.error(f"Error retrieving context: {e}")
        logging.error(traceback.format_exc())
        return ""

def convert_user_id_to_key(user_id: str) -> tuple:
    """Convert user_id string to tuple format (patient_id, session_id)"""
    if "_" in user_id:
        parts = user_id.split("_", 1)
        return (parts[0], parts[1])
    else:
        # For backward compatibility, use user_id as patient_id and "default" as session_id
        return (user_id, "default")

def add_health_record(user_key: tuple, vitals: Dict[str, float], user_health_data: Dict):
    entry = {
        "timestamp": datetime.now().isoformat()
    }
    entry.update(vitals)
    user_health_data.setdefault(user_key, []).append(entry)


def analyze_health_score(health_data: Dict[str, Any]) -> Dict:
    """Analyze health score data"""
    try:
        # Log the input data for debugging
        logging.info(f"Health data received: {json.dumps(health_data)}")

        # Process the health data to ensure it's in the correct format
        processed_data = {}
        for key, value in health_data.items():
            # Handle test results with string values
            if key in ["Malaria", "Hepatitis B", "Widal Test", "Voluntary Serology"]:
                # Ensure "Unknown" is preserved as "Unknown"
                if value == "Unknown" or value is None or value == "" or value == "null":
                    processed_data[key] = "Unknown"
                else:
                    processed_data[key] = value
            # Handle "Unknown" values for all other fields
            elif value == "Unknown":
                # Skip Unknown values completely - don't add them to processed_data
                # This ensures they won't be evaluated at all
                continue
            # Handle None values
            elif value is None or value == "" or value == "null":
                # Skip null values completely - don't add them to processed_data
                # This ensures they won't be evaluated at all
                continue
            # Convert numeric values to float
            else:
                try:
                    processed_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_data[key] = value

        logging.info(f"Processed health data: {json.dumps(processed_data)}")

        # Initialize the custom health score tool directly
        logging.info("Initializing CustomHealthScoreAnalysisTool")

        # Create a custom subclass to override the generate_report method
        class CustomHealthScoreAnalysisTool(HealthScoreAnalysisTool):
            def generate_report(self, health_data: dict) -> dict:
                total_score = 0
                max_score = 0
                vitals_needing_improvement = []
                improvement_tips = []

                # Track categories to avoid repetitive tips
                tip_categories = {
                    "doctor_visit": False,  # For serious issues requiring medical attention
                    "breathing": False,     # For respiratory-related tips
                    "diet": False,          # For nutrition-related tips
                    "exercise": False,      # For physical activity tips
                    "blood_pressure": False # For blood pressure related tips
                }

                # Track abnormal test results to consolidate recommendations
                abnormal_tests = []

                # We've already filtered out null values in the analyze_health_score function
                # This loop will only process non-null values
                for key, value in health_data.items():

                    # Handle test results properly
                    if key in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]:
                        max_score += 5
                        if isinstance(value, str):
                            if value.lower() == "negative":
                                total_score += 5
                            elif value.lower() == "unknown":
                                # Don't count Unknown as needing improvement
                                # Also don't add to max_score to avoid penalizing for unknown values
                                max_score -= 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Positive)")
                                abnormal_tests.append(key)
                    # Handle other metrics using the parent class logic
                    elif key == "Weight (BMI)":
                        # This field stores the BMI value, not the weight
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value < 18.5:
                                vitals_needing_improvement.append(f"{key} (Low)")
                                if not tip_categories["diet"]:
                                    improvement_tips.append("🥗 Consider adding more nutrient-dense foods like nuts, avocados, and protein-rich meals to help you reach a healthier weight.")
                                    tip_categories["diet"] = True
                            elif 18.5 <= value <= 24.9:
                                total_score += 10
                            elif 25 <= value <= 29.9:
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Moderately High)")
                                if not tip_categories["exercise"]:
                                    improvement_tips.append("🚶‍♀️ Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight.")
                                    tip_categories["exercise"] = True
                            else:
                                vitals_needing_improvement.append(f"{key} (High)")
                                if not tip_categories["diet"] and not tip_categories["exercise"]:
                                    improvement_tips.append("💪 Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle.")
                                    tip_categories["diet"] = True
                                    tip_categories["exercise"] = True
                    elif key == "Glucose":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 70 <= value <= 100:
                                total_score += 10
                            elif 100 < value <= 125:
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Moderately High)")
                                if not tip_categories["diet"]:
                                    improvement_tips.append("🍎 To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.")
                                    tip_categories["diet"] = True
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                if not tip_categories["doctor_visit"]:
                                    improvement_tips.append("🩺 Your glucose levels warrant a discussion with your healthcare provider who can recommend appropriate monitoring and management strategies.")
                                    tip_categories["doctor_visit"] = True
                    elif key == "SpO2":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value >= 95:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Low)")
                                if not tip_categories["breathing"]:
                                    improvement_tips.append("🫁 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.")
                                    tip_categories["breathing"] = True
                    elif key == "Temperature":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 36.5 <= value <= 37.5:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                if value > 37.5:
                                    improvement_tips.append("🌡️ For your elevated temperature, ensure you're well-hydrated, rest adequately, and monitor for any changes. If it persists beyond 48 hours, consult your doctor.")
                                else:
                                    improvement_tips.append("🌡️ Your temperature is running a bit low. Keep warm, stay hydrated, and monitor for any other symptoms that might accompany this reading.")
                    elif key == "ECG (Heart Rate)":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 60 <= value <= 100:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                if value > 100:
                                    improvement_tips.append("❤️ For your elevated heart rate, try practicing mindfulness meditation, limiting caffeine, and ensuring adequate rest. If it remains consistently high, consult your doctor.")
                                else:
                                    improvement_tips.append("❤️ Your heart rate is on the lower side. This can be normal for physically fit individuals, but monitor for any symptoms like dizziness or fatigue.")
                    elif key == "Blood Pressure (Systolic)" or key == "Blood Pressure (Diastolic)":
                        max_score += 5
                        is_systolic = key == "Blood Pressure (Systolic)"
                        normal_range = (90, 120) if is_systolic else (60, 80)

                        if isinstance(value, (int, float)):
                            if normal_range[0] <= value <= normal_range[1]:
                                total_score += 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")

                                # Only add blood pressure tip if we haven't already
                                if not tip_categories["blood_pressure"]:
                                    if (is_systolic and value > normal_range[1]) or (not is_systolic and value > normal_range[1]):
                                        improvement_tips.append("🩸 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.")
                                    else:
                                        improvement_tips.append("🩸 Your blood pressure reading is on the lower side. Stay well-hydrated, rise slowly from sitting/lying positions, and discuss with your doctor if you experience dizziness or fatigue.")

                                    tip_categories["blood_pressure"] = True
                    elif key == "Perfusion_index":
                        # Only evaluate if value is not None
                        if value is not None:
                            max_score += 5
                            if isinstance(value, (int, float)) and 0.02 <= value <= 20:
                                total_score += 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                improvement_tips.append("💓 To improve peripheral circulation reflected in your perfusion index, stay well-hydrated, avoid prolonged sitting, and consider gentle exercises like walking or swimming.")
                    elif key == "Fev":
                        # Only evaluate if value is not None
                        if value is not None:
                            max_score += 5
                            if isinstance(value, (int, float)) and value >= 80:
                                total_score += 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Low)")
                                if not tip_categories["breathing"]:
                                    improvement_tips.append("🫁 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor.")
                                    tip_categories["breathing"] = True

                # Add consolidated recommendation for abnormal test results
                if abnormal_tests:
                    improvement_tips.append(f"🩺 Your test results for {', '.join(abnormal_tests)} require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.")

                # Normalize score
                final_score = round((total_score / max_score) * 100) if max_score > 0 else 0

                # Health status logic
                if final_score >= 85:
                    status = "Excellent"
                elif final_score >= 70:
                    status = "Good"
                elif final_score >= 50:
                    status = "Fair"
                else:
                    status = "Poor"

                # If no improvement tips were generated but there are vitals needing improvement
                if not improvement_tips and vitals_needing_improvement:
                    improvement_tips.append("🌟 Some of your health metrics could use attention. Focus on a balanced diet, regular exercise, and adequate rest to improve your overall health.")

                # If everything is normal
                if not improvement_tips and not vitals_needing_improvement:
                    improvement_tips.append("🌟 Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!")

                return {
                    "Total Score": final_score,
                    "Health Status": status,
                    "Vitals Needing Improvement": vitals_needing_improvement if vitals_needing_improvement else ["None"],
                    "Improvement Tips": improvement_tips
                }

        # Use the custom tool to generate the report
        custom_tool = CustomHealthScoreAnalysisTool()
        result = custom_tool.generate_report(processed_data)
        logging.info(f"Health score report generated: {json.dumps(result)}")

        return result
    except Exception as e:
        error_msg = f"Failed to analyze health score: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

def process_vital_signs(vital_signs: Dict[str, float]) -> Dict:
    """Process vital signs data and return analysis"""
    try:
        # Format data for the tool
        vital_signs_json = json.dumps({"data": vital_signs})
        logging.info(f"Processing vital signs: {vital_signs_json}")

        # Use the vital sign monitoring tool
        result = monitor_vital_signs(vital_signs_json)
        logging.info(f"Vital signs monitoring result: {result}")

        # Check for abnormal patterns
        alerts = []
        severity = "Normal"
        if vital_signs.get("Glucose") and vital_signs["Glucose"] > 140:
            alerts.append("⚠️ High glucose levels detected, Possible hyperglycemia. Consider consulting a doctor.")
            severity = "Critical"
        if vital_signs.get("SpO2") and vital_signs["SpO2"] < 95:
            alerts.append("⚠️ Low SpO2 levels detected, Possible hypoxemia. Ensure proper ventilation.")
            severity = "Critical"
        if vital_signs.get("Heart_Rate") and vital_signs["Heart_Rate"] > 100:
            alerts.append("⚠️ High heart rate detected. Practice stress management.")
            severity = "Caution"
        if vital_signs.get("Temperature") and vital_signs["Temperature"] > 37.5:
            alerts.append("⚠️ Fever detected. Stay hydrated and consult a doctor if it persists.")
            severity = "Caution"

        alert_text = "\n".join(alerts) if alerts else "✅ No abnormal patterns detected."

        return {
            "analysis": result,
            "alerts": alert_text,
            "suggest_consultation": len(alerts) > 0,
            "recommendation": "Please consult your doctor." if severity == "Critical" else "Continue monitoring regularly."
        }
    except Exception as e:
        error_msg = f"Failed to process vital signs: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

def get_previous_intent(user_key) -> str:
    """Extract the most recent intent from chat history"""
    if user_key not in chat_histories or len(chat_histories[user_key]) < 3:
        return ""

    # Look at the last assistant message
    for message in reversed(chat_histories[user_key]):
        if message.get("role") == "assistant":
            content = message.get("content", "").lower()

            # Check for intent patterns in the message
            if "would you like to analyze your health score" in content:
                return "health_score"
            elif "would you like to generate a real-time health score" in content or "would you like to see your real-time health score" in content:
                return "realtime_health_score"
            elif "would you like to enter your vital signs" in content:
                return "vital_signs"
            elif "would you like to analyze your kidney function" in content:
                return "kidney_function"
            elif "would you like to analyze your lipid profile" in content:
                return "lipid_profile"
            elif "would you like to analyze your lung capacity" in content or "would you like to check your respiratory health" in content:
                return "lung_capacity"
            elif "would you like to start a comprehensive health consultation" in content or "would you like to start a health consultation" in content or "would you like me to do that now" in content:
                return "health_consultation"
            elif "would you like me to recommend health devices" in content or "would you like to see device recommendations" in content:
                return "device_recommendations"
            elif any(
                phrase in content
                for phrase in [
                    "would you like to track diabetes",
                    "would you like to track hypertension",
                    "would you like to track asthma",
                    "would you like to track heart disease",
                    "would you like to track kidney disease",
                    "would you like to track your chronic condition",
                ]
            ):
                return "chronic_tracker"

    return ""

def analyze_health_trends(user_key) -> str:
    """Analyze trends in user's health data over time"""
    if user_key not in user_health_data:
        return ""

    user_data = user_health_data[user_key]
    trend_analysis = "📈 **Health Trends Analysis**\n\n"
    has_trends = False

    # Check if we have multiple health score entries
    if "health_score" in user_data:
        # For now, we're just showing the most recent score
        # In a real implementation, you would store historical data and show trends
        score_data = user_data["health_score"]
        trend_analysis += f"**Health Score**: {score_data['result'].get('Total Score', 0)} ({score_data['result'].get('Health Status', 'Unknown')})\n"
        trend_analysis += f"Last measured: {score_data['timestamp'][:10]}\n\n"
        has_trends = True

    # Check vital signs trends
    if "vital_signs" in user_data:
        vital_data = user_data["vital_signs"]
        trend_analysis += "**Vital Signs**:\n"
        for key, value in vital_data['data'].items():
            trend_analysis += f"- {key}: {value}\n"
        trend_analysis += f"Last measured: {vital_data['timestamp'][:10]}\n\n"
        has_trends = True

    # Add kidney function if available
    if "kidney_function" in user_data:
        kidney_data = user_data["kidney_function"]
        trend_analysis += f"**Kidney Function**: {kidney_data['result'].get('overall_health', 'Unknown')}\n"
        trend_analysis += f"Last measured: {kidney_data['timestamp'][:10]}\n\n"
        has_trends = True

    # Add lipid profile if available
    if "lipid_profile" in user_data:
        lipid_data = user_data["lipid_profile"]
        trend_analysis += f"**Lipid Profile**: ASCVD Risk - {lipid_data['result'].get('ascvd_risk', 'Unknown')}\n"
        trend_analysis += f"Last measured: {lipid_data['timestamp'][:10]}\n\n"
        has_trends = True

    # Add lung capacity if available
    if "lung_capacity" in user_data:
        lung_data = user_data["lung_capacity"]
        trend_analysis += f"**Respiratory Health**: Risk Level - {lung_data['result'].get('respiratory_risk_level', 'Unknown')}\n"

        # Add potential conditions if any identified
        conditions = lung_data['result'].get('potential_conditions', [])
        if conditions and conditions != ["No specific respiratory conditions identified"]:
            trend_analysis += f"Conditions: {', '.join(conditions)}\n"

        trend_analysis += f"Last measured: {lung_data['timestamp'][:10]}\n\n"
        has_trends = True

    if has_trends:
        trend_analysis += "**Summary**:\n"
        trend_analysis += "I'm tracking your health data over time. As you continue to provide measurements, I'll be able to show you trends and progress toward your health goals.\n\n"
        trend_analysis += "Would you like to update any of your health measurements today?"
        return trend_analysis

    return ""

def process_kidney_function(data: Dict[str, float]) -> Dict:
    """Analyze kidney function data using the updated tool"""
    try:
        logging.info(f"Processing kidney function data: {json.dumps(data)}")
        # The updated kidney_function_analysis_tool returns a more comprehensive result
        # that includes analysis, overall_health, confidence_level, missing_parameters, and recommendations
        result = kidney_function_analysis_tool(data)

        # No need to format the output as the tool now returns a well-structured result
        return result
    except Exception as e:
        error_msg = f"Failed to process kidney function: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def process_lung_capacity(data: Dict[str, Any]) -> Dict:
    """Analyze lung capacity data using the lung capacity analyzer tool"""
    try:
        logging.info(f"Processing lung capacity data: {json.dumps(data)}")
        # Format data for the tool
        data_json = json.dumps({"data": data})

        # Use the lung capacity analyzer tool
        result_json = lung_capacity_analyzer_tool.func(data_json)
        result = json.loads(result_json)

        logging.info(f"Lung capacity analysis result: {json.dumps(result)}")
        return result
    except Exception as e:
        error_msg = f"Failed to process lung capacity data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def process_test_results(data: Dict[str, Any]) -> Dict:
    """Interpret test results using the test results interpreter tool"""
    try:
        logging.info(f"Processing test results data: {json.dumps(data)}")
        print(f"TEST RESULTS DATA: {json.dumps(data, indent=2)}")

        # Convert data to JSON string for the tool
        data_json = json.dumps(data)

        # Use the enhanced test results interpreter tool
        from tools.tools_test_results import interpret_test_results
        result_json = interpret_test_results(data_json)
        result = json.loads(result_json)

        logging.info(f"Test results interpretation: {json.dumps(result)}")
        return result
    except Exception as e:
        error_msg = f"Failed to process test results: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def process_realtime_health_score(data: Dict[str, Any]) -> Dict:
    """Process real-time health score data using the enhanced HealthScoreAnalysisTool"""
    try:
        logging.info(f"Processing real-time health score data: {json.dumps(data)}")

        # Process data to handle "Unknown" values
        processed_data = {}
        for key, value in data.items():
            # Skip "Unknown" values
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle test results with string values
            elif key in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]:
                processed_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    processed_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_data[key] = value

        # Initialize the enhanced health score tool
        health_tool = HealthScoreAnalysisTool()

        # Process the health data
        result = health_tool.generate_report(processed_data)

        logging.info(f"Real-time health score result: {json.dumps(result)}")
        return result
    except Exception as e:
        error_msg = f"Failed to process real-time health score: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def check_health_alerts(user_key) -> str:
    """Check for potential health issues based on user's health data"""
    if user_key not in user_health_data:
        return ""

    alerts = []
    user_data = user_health_data[user_key]

    # Check vital signs for critical values
    if "vital_signs" in user_data:
        vital_data = user_data["vital_signs"]["data"]

        # Check blood pressure
        if "Blood Pressure (Systolic)" in vital_data and "Blood Pressure (Diastolic)" in vital_data:
            systolic = vital_data["Blood Pressure (Systolic)"]
            diastolic = vital_data["Blood Pressure (Diastolic)"]

            if isinstance(systolic, (int, float)) and isinstance(diastolic, (int, float)):
                if systolic > 180 or diastolic > 120:
                    alerts.append("🚨 **CRITICAL**: Your blood pressure readings indicate hypertensive crisis. Seek immediate medical attention.")
                elif systolic > 140 or diastolic > 90:
                    alerts.append("⚠️ Your blood pressure readings indicate hypertension. Consider consulting with your healthcare provider.")

        # Check glucose
        if "Glucose" in vital_data:
            glucose = vital_data["Glucose"]
            if isinstance(glucose, (int, float)):
                if glucose > 200:
                    alerts.append("⚠️ Your glucose level is significantly elevated. This may indicate diabetes or prediabetes. Consider consulting with your healthcare provider.")
                elif glucose < 70:
                    alerts.append("⚠️ Your glucose level is low. This may indicate hypoglycemia. Consider consulting with your healthcare provider.")

        # Check oxygen saturation
        if "SpO2" in vital_data:
            spo2 = vital_data["SpO2"]
            if isinstance(spo2, (int, float)) and spo2 < 92:
                alerts.append("⚠️ Your oxygen saturation is below normal levels. This may indicate respiratory issues. Consider consulting with your healthcare provider.")

    # Check health score for concerning values
    if "health_score" in user_data:
        score = user_data["health_score"]["result"].get("Total Score", 0)
        if score < 50:
            alerts.append("⚠️ Your overall health score is in the 'Poor' range. Consider scheduling a comprehensive health check-up.")

    # Check real-time health score for concerning values
    if "realtime_health_score" in user_data:
        rt_score = user_data["realtime_health_score"]["result"].get("Total Score", 0)
        if rt_score < 50:
            alerts.append("⚠️ Your real-time health score is in the 'Poor' range. Several health metrics need attention.")

        # Check category scores
        if "Category Scores" in user_data["realtime_health_score"]["result"]:
            category_scores = user_data["realtime_health_score"]["result"]["Category Scores"]
            for category, score in category_scores.items():
                if score < 40:
                    alerts.append(f"⚠️ Your {category} score is critically low at {score}%. This area requires immediate attention.")

    # Check kidney function
    if "kidney_function" in user_data:
        kidney_health = user_data["kidney_function"]["result"].get("overall_health", "")
        if "poor" in kidney_health.lower() or "concerning" in kidney_health.lower():
            alerts.append("⚠️ Your kidney function test indicates potential issues. Consider consulting with a nephrologist.")

    # Check lipid profile
    if "lipid_profile" in user_data:
        ascvd_risk = user_data["lipid_profile"]["result"].get("ascvd_risk", "")
        if "high" in ascvd_risk.lower():
            alerts.append("⚠️ Your lipid profile indicates a high cardiovascular risk. Consider consulting with your healthcare provider about strategies to reduce this risk.")

    # Check lung capacity
    if "lung_capacity" in user_data:
        risk_level = user_data["lung_capacity"]["result"].get("respiratory_risk_level", "")
        conditions = user_data["lung_capacity"]["result"].get("potential_conditions", [])

        if "high" in risk_level.lower():
            alerts.append("⚠️ Your lung capacity test indicates a high respiratory risk. Consider consulting with a pulmonologist.")

        if any("COPD" in condition for condition in conditions):
            alerts.append("⚠️ Your lung function test shows patterns consistent with COPD. Follow up with a healthcare provider for proper management.")

        if any("asthma" in condition.lower() for condition in conditions):
            alerts.append("⚠️ Your lung function test shows patterns consistent with asthma. Consider consulting with an allergist or pulmonologist.")

    if alerts:
        return "\n\n**Health Alerts**\n" + "\n".join(alerts)

    return ""

def process_agent_query(query: str, patient_id: str, session_id: str, model_name: str) -> Dict:
    """Process a query through the agent, detecting intent and using appropriate tools"""
    try:
        user_key = (patient_id, session_id)

        # Get relevant context from vector store
        context = retrieve_context(query, model_name)
        logging.info(f"Retrieved context: {context[:100]}...")

        # Detect intent from the query
        query_lower = query.lower()

        # Prepare system prompt based on detected intent
        system_prompt = "You are Dr. Deuce, a certified and authorized medical assistant. You assist users by analyzing their health data, monitoring vitals, and providing health consultations. When giving recommendations or health advice, ALWAYS use the user's actual health data if available. Personalize your responses based on their specific health metrics rather than giving generic advice."

        # Add context to the system prompt
        if context:
            system_prompt += f"\n\nRelevant information: {context}"

        # Check for any health alerts that should be brought to the user's attention
        health_alerts = check_health_alerts(user_key)
        if health_alerts:
            system_prompt += f"\n\nIMPORTANT HEALTH ALERTS: {health_alerts}\nMake sure to mention these alerts to the user if they are relevant to their query."

        # Always include user health data in the system prompt
        health_data_context = ""
        if user_key in user_health_data:
            # Add a header for the health data section
            health_data_context += "\n\nUser's Health Data Summary:\n"

            # Add vital signs data if available
            if "vital_signs" in user_health_data[user_key]:
                vital_data = user_health_data[user_key]["vital_signs"]
                health_data_context += "\n📊 Vital Signs:\n"
                health_data_context += f"Test date: {vital_data['timestamp'][:10]}\n"

                # Add vital signs
                for key, value in vital_data['data'].items():
                    health_data_context += f"- {key}: {value}\n"

                # Add alerts if any
                if "alerts" in vital_data['result'] and vital_data['result']['alerts']:
                    health_data_context += f"Alerts: {vital_data['result']['alerts']}\n"

            # Add health score data if available
            if "health_score" in user_health_data[user_key]:
                score_data = user_health_data[user_key]["health_score"]
                health_data_context += "\n🏆 Health Score:\n"
                health_data_context += f"Test date: {score_data['timestamp'][:10]}\n"
                health_data_context += f"Total Score: {score_data['result'].get('Total Score', 'Unknown')} Health Status: {score_data['result'].get('Health Status', 'Unknown')}\n\n"

                # Add vitals needing improvement
                if "Vitals Needing Improvement" in score_data['result']:
                    vitals = score_data['result']['Vitals Needing Improvement']
                    health_data_context += "Vitals Needing Improvement:\n"
                    if isinstance(vitals, list):
                        if vitals == ["None"]:
                            health_data_context += "None\n\n"
                        else:
                            for vital in vitals:
                                health_data_context += f"- {vital}\n"
                            health_data_context += "\n"
                    else:
                        health_data_context += f"{vitals}\n\n"

                # Add improvement tips
                if "Improvement Tips" in score_data['result']:
                    tips = score_data['result']['Improvement Tips']
                    health_data_context += "Improvement Tips:\n"
                    if isinstance(tips, list):
                        if not tips:
                            health_data_context += "None\n"
                        else:
                            for tip in tips:
                                health_data_context += f"- {tip}\n"
                    else:
                        health_data_context += f"{tips}\n"

            # Add kidney function data if available
            if "kidney_function" in user_health_data[user_key]:
                kidney_data = user_health_data[user_key]["kidney_function"]
                health_data_context += "\n🧪 Kidney Function:\n"
                health_data_context += f"Test date: {kidney_data['timestamp'][:10]}\n"
                health_data_context += f"Overall health: {kidney_data['result'].get('overall_health', 'Unknown')}\n"
                health_data_context += f"Confidence level: {kidney_data['result'].get('confidence_level', 'Unknown')}\n"

                # Add analysis items
                analysis_items = kidney_data['result'].get('analysis', [])
                if analysis_items:
                    health_data_context += "Analysis:\n"
                    if isinstance(analysis_items, list):
                        for item in analysis_items:
                            health_data_context += f"- {item}\n"
                    else:
                        health_data_context += f"{analysis_items}\n"

                # Add recommendations if available
                if "recommendations" in kidney_data['result'] and kidney_data['result']['recommendations']:
                    health_data_context += "Recommendations:\n"
                    for rec in kidney_data['result']['recommendations']:
                        health_data_context += f"- {rec}\n"

            # Add lipid profile data if available
            if "lipid_profile" in user_health_data[user_key]:
                lipid_data = user_health_data[user_key]["lipid_profile"]
                health_data_context += "\n💉 Lipid Profile:\n"
                health_data_context += f"Test date: {lipid_data['timestamp'][:10]}\n"

                # Add classification
                classification = lipid_data['result'].get('classification', {})
                if classification:
                    health_data_context += "Classification:\n"
                    for component, level in classification.items():
                        health_data_context += f"- {component.replace('_', ' ').title()}: {level.title()}\n"

                # Add risk assessment
                health_data_context += f"ASCVD Risk: {lipid_data['result'].get('ascvd_risk', 'Unknown')}\n"

                # Add parameter explanations if available
                if "parameter_explanations" in lipid_data['result'] and lipid_data['result']['parameter_explanations']:
                    health_data_context += "Parameter Explanations:\n"
                    for param in lipid_data['result']['parameter_explanations'][:2]:  # Limit to 2 for brevity
                        health_data_context += f"- {param['parameter']}: {param['status_explanation']}\n"

                # Add diet plan information if available
                if "diet_plan" in lipid_data['result'] and lipid_data['result']['diet_plan']:
                    diet_plan = lipid_data['result']['diet_plan']
                    health_data_context += f"Diet Plan: {diet_plan.get('title', 'Heart-Healthy Diet Plan')}\n"
                    health_data_context += f"Overview: {diet_plan.get('overview', '')}\n"

                # Add cardiac health plan information if available
                if "cardiac_health_plan" in lipid_data['result'] and lipid_data['result']['cardiac_health_plan']:
                    cardiac_plan = lipid_data['result']['cardiac_health_plan']
                    health_data_context += f"Cardiac Health Plan: {cardiac_plan.get('title', 'Personalized Cardiac Health Plan')}\n"
                    health_data_context += f"Follow-up: {cardiac_plan.get('follow_up', '')}\n"

                # Add recommendations if available
                if "recommendations" in lipid_data['result'] and lipid_data['result']['recommendations']:
                    health_data_context += "Recommendations:\n"
                    for rec in lipid_data['result']['recommendations'][:3]:  # Limit to 3 for brevity
                        health_data_context += f"- {rec}\n"

            # Add instructions for the agent to use this data
            health_data_context += "\nIMPORTANT: Always use the above health data when providing recommendations or answering health-related questions. Personalize your responses based on this data."

        # Add health data context to system prompt if available
        if health_data_context:
            system_prompt += f"\n\nUser's health data for reference:{health_data_context}"
            logging.info(f"Added health data context to system prompt for user {user_key}")

        # Initialize chat history if first interaction
        if user_key not in chat_histories:
            chat_histories[user_key] = [{"role": "system", "content": system_prompt}]
            chat_titles[user_key] = generate_chat_title(query)
        else:
            # Update system prompt with latest health data context
            chat_histories[user_key][0]["content"] = system_prompt

        # Add user query to history
        chat_histories[user_key].append({"role": "user", "content": query})

        # Keep chat history within limit
        if len(chat_histories[user_key]) > MAX_HISTORY_LENGTH:
            # Keep the system message and the most recent messages
            system_message = chat_histories[user_key][0]
            chat_histories[user_key] = [system_message] + chat_histories[user_key][-(MAX_HISTORY_LENGTH-1):]

        # Get response from Ollama
        response = ollama.chat(model=model_name, messages=chat_histories[user_key])
        model_response = response["message"]["content"]

        # Enhanced intent detection with more sophisticated pattern matching
        tool_response = ""
        tools_used = []
        # Define intent patterns with weights and related terms
        intent_patterns = {
            "recommendation": {
                "keywords": ["recommendation", "advice", "suggest", "tips", "what should i do", "help me", "guidance",
                            "what can i do", "how can i improve", "how to", "best way to"],
                "weight": 1.0,
                "handler": "handle_recommendation_intent"
            },
            "health_score": {
                "keywords": ["health score", "analyze my health", "health analysis", "overall health", "how healthy am i",
                            "health assessment", "evaluate my health", "health status", "health check"],
                "weight": 1.0,
                "handler": "handle_health_score_intent"
            },
            "realtime_health_score": {
                "keywords": ["real-time health score", "realtime health score", "real time health score", "dynamic health score",
                            "holistic health score", "comprehensive health score", "combined health score",
                            "lifestyle and vitals", "complete health picture", "health dashboard"],
                "weight": 1.2,  # Higher weight for this specialized intent
                "handler": "handle_realtime_health_score_intent"
            },
            "vital_signs": {
                "keywords": ["vital signs", "monitor vitals", "check vitals", "blood pressure", "heart rate",
                            "temperature", "oxygen", "spo2", "pulse", "vitals"],
                "weight": 1.0,
                "handler": "handle_vital_signs_intent"
            },
            "kidney_function": {
                "keywords": ["kidney function", "kidney test", "renal function", "kidney health", "creatinine",
                            "egfr", "bun", "kidney disease", "kidney problems"],
                "weight": 1.0,
                "handler": "handle_kidney_function_intent"
            },
            "lipid_profile": {
                "keywords": ["lipid profile", "cholesterol test", "lipid test", "cholesterol", "triglycerides",
                            "hdl", "ldl", "heart health", "cardiovascular", "cardiac health", "heart disease risk",
                            "heart diet", "heart-healthy diet", "cardiac risk", "ascvd risk", "heart attack risk"],
                "weight": 1.0,
                "handler": "handle_lipid_profile_intent"
            },
            "lung_capacity": {
                "keywords": ["lung capacity", "respiratory", "breathing", "spirometry", "fev1", "fvc", "copd",
                            "asthma", "pulmonary", "lung function", "lung health", "breath test", "shortness of breath",
                            "breathing difficulty", "lung disease", "respiratory health"],
                "weight": 1.0,
                "handler": "handle_lung_capacity_intent"
            },
            "liver_function": {
                "keywords": ["liver function", "liver test", "hepatic function", "liver health", "alt", "ast", "alp",
                            "bilirubin", "liver enzymes", "hepatitis", "liver disease", "liver panel", "lft",
                            "liver function test", "hepatic panel", "liver profile", "liver screening"],
                "weight": 1.0,
                "handler": "handle_liver_function_intent"
            },
            "health_consultation": {
                "keywords": ["health consultation", "consult", "medical advice", "talk to doctor", "professional opinion",
                            "medical consultation", "doctor advice", "clinical advice", "appointment", "schedule",
                            "checkup", "follow-up", "follow up", "doctor visit", "specialist", "when should i see a doctor",
                            "care plan", "care coordination", "medical appointment", "screening", "test schedule",
                            "recommended tests", "proactive care", "preventive care", "preventative care", "health plan"],
                "weight": 1.0,
                "handler": "handle_consultation_intent"
            },

            "health_trends": {
                "keywords": ["trends", "progress", "changes", "over time", "improving", "getting better", "getting worse",
                            "track", "history", "compare", "improvement", "deterioration"],
                "weight": 1.0,
                "handler": "handle_health_trends_intent"
            },
            "device_recommendations": {
                "keywords": ["device", "devices", "gadget", "gadgets", "health device", "health devices", "health gadget",
                            "health gadgets", "monitor", "monitoring device", "wearable", "wearables", "smart device",
                            "smart watch", "fitness tracker", "health tracker", "connected device", "connected health",
                            "recommend device", "suggest device", "what device", "which device", "home monitoring"],
                "weight": 1.0,
                "handler": "handle_device_recommendations_intent"
            },
            "chronic_tracker": {
                "keywords": ["chronic tracker", "track condition", "monitor condition", "track diabetes", "track hypertension",
                            "track asthma", "track heart disease", "track kidney disease", "diabetes tracking",
                            "hypertension tracking", "asthma tracking", "heart disease tracking", "kidney disease tracking",
                            "chronic condition", "chronic management", "manage diabetes", "manage hypertension",
                            "manage asthma", "manage heart disease", "manage kidney disease", "chronic care",
                            "diabetes management", "blood sugar tracking", "glucose monitoring", "blood pressure tracking",
                            "peak flow tracking", "track my condition", "monitor my health", "track my chronic condition"],
                "weight": 1.0,
                "handler": "handle_chronic_tracker"
            },
            "yes_confirmation": {
                "keywords": ["yes", "sure", "okay", "ok", "yep", "yeah", "proceed", "let's do it", "go ahead"],
                "weight": 1.5,  # Higher weight for confirmation
                "handler": "handle_confirmation"
            },
            "no_confirmation": {
                "keywords": ["no", "nope", "nah", "not now", "maybe later", "not interested", "skip"],
                "weight": 1.5,  # Higher weight for confirmation
                "handler": "handle_decline"
            }
        }

        # Detect intents with scoring
        detected_intents = {}
        for intent, config in intent_patterns.items():
            score = 0
            for keyword in config["keywords"]:
                if keyword in query_lower:
                    # Add score based on keyword match and weight
                    score += config["weight"]
                    # Add extra score for exact matches or standalone keywords
                    if keyword == query_lower.strip() or f" {keyword} " in f" {query_lower} ":
                        score += 0.5

            if score > 0:
                detected_intents[intent] = score

        # Sort intents by score
        sorted_intents = sorted(detected_intents.items(), key=lambda x: x[1], reverse=True)

        # Handle the highest scoring intent
        if sorted_intents:
            primary_intent = sorted_intents[0][0]
            logging.info(f"Detected primary intent: {primary_intent} with score {sorted_intents[0][1]}")

            # Check for confirmation intent (special case)
            if primary_intent == "yes_confirmation":
                # Look for the most recent intent in chat history
                previous_intent = get_previous_intent(user_key)
                if previous_intent:
                    primary_intent = previous_intent
                    logging.info(f"Confirmation detected, using previous intent: {primary_intent}")

                    # Special handling for health consultation confirmation
                    if previous_intent == "health_consultation" and user_key in user_health_data and user_health_data[user_key]:
                        # Add to chat history
                        chat_histories[user_key].append({"role": "user", "content": "Yes, I'd like a comprehensive health consultation."})

                        # instead of awaiting it in this synchronous context
                        consultation_input = {
                            "user_id": f"{user_key[0]}_{user_key[1]}",  # Convert tuple to string for the tool
                            "health_data": user_health_data[user_key]
                        }

                        # Run the comprehensive health consultation tool directly
                        consultation_result_json = automated_health_consultation_tool.func(json.dumps(consultation_input))
                        consultation_result = json.loads(consultation_result_json)

                    # Special handling for device recommendations confirmation
                    elif previous_intent == "device_recommendations" and user_key in user_health_data and user_health_data[user_key]:
                        # Add to chat history
                        chat_histories[user_key].append({"role": "user", "content": "Yes, I'd like to see device recommendations based on my health data."})

                        # Get the most recent health data
                        health_data = None
                        if "health_score" in user_health_data[user_key]:
                            health_data = user_health_data[user_key]["health_score"]["data"]
                        elif "realtime_health_score" in user_health_data[user_key]:
                            health_data = user_health_data[user_key]["realtime_health_score"]["data"]
                        else:
                            # Use default health data structure if no data is available
                            health_data = get_default_health_data()["data"]

                        # Format data for the tool
                        health_data_json = json.dumps({"data": health_data})

                        # Run the device recommender tool directly
                        device_result_json = recommend_health_devices(health_data_json)
                        device_result = json.loads(device_result_json)

                        # Save device recommendations to user health data store
                        user_health_data[user_key]["device_recommendations"] = {
                            "data": health_data,
                            "result": device_result,
                            "timestamp": datetime.now().isoformat()
                        }

                        # Create a summary of the recommendations for the assistant's response
                        summary = "Based on your health data, here are my device recommendations:\n\n"

                        # Add the recommendation summary
                        if "recommendation_summary" in device_result:
                            summary += device_result["recommendation_summary"]
                        else:
                            # Fallback to a more basic summary if the detailed one isn't available
                            missing_vitals = device_result.get("missing_vitals", [])
                            abnormal_vitals = device_result.get("abnormal_vitals", [])

                            if missing_vitals:
                                summary += "**Missing vital measurements:**\n"
                                for vital in missing_vitals:
                                    summary += f"- {vital}\n"
                                summary += "\n"

                            if abnormal_vitals:
                                summary += "**Abnormal vital measurements:**\n"
                                for vital in abnormal_vitals:
                                    summary += f"- {vital}\n"
                                summary += "\n"

                            # Add device recommendations
                            single_devices = device_result.get("single_device_recommendations", [])
                            multi_devices = device_result.get("multi_device_recommendations", [])

                            if multi_devices:
                                summary += "**Recommended multi-function devices:**\n"
                                for device in multi_devices[:2]:  # Limit to top 2
                                    summary += f"- {device['name']} - {device['description']}\n"
                                summary += "\n"

                            if single_devices:
                                summary += "**Recommended single-function devices:**\n"
                                for device in single_devices[:3]:  # Limit to top 3
                                    summary += f"- {device['name']} - {device['description']}\n"

                        # Add assistant message
                        chat_histories[user_key].append({"role": "assistant", "content": summary})

                        # Return the device recommendations result
                        return {
                            "response": summary,
                            "chat_title": chat_titles[user_key],
                            "chat_history": chat_histories[user_key]
                        }

                # Check if the last assistant message contains a device recommendation prompt (from health analysis)
                elif user_key in chat_histories and len(chat_histories[user_key]) > 1:
                    last_assistant_message = None
                    for message in reversed(chat_histories[user_key]):
                        if message["role"] == "assistant":
                            last_assistant_message = message["content"]
                            break

                    # Check if the last message contains a device recommendation prompt
                    if last_assistant_message and "would you like to see health monitoring devices from turbomedics" in last_assistant_message.lower():
                        # Handle device recommendation confirmation from health analysis
                        chat_histories[user_key].append({"role": "user", "content": "Yes, I'd like to see health monitoring devices."})

                        # Use the device confirmation tool
                        confirmation_result = json.loads(confirm_device_recommendation(json.dumps({"confirmed": True})))

                        # Create response with TurboMedics redirect
                        response_message = confirmation_result.get("message", "")
                        redirect_message = confirmation_result.get("redirect_message", "")

                        if redirect_message:
                            response_message += f"\n\n{redirect_message}"

                        # Add store features if available
                        if "additional_info" in confirmation_result and "store_features" in confirmation_result["additional_info"]:
                            response_message += "\n\n**Store Features:**\n"
                            for feature in confirmation_result["additional_info"]["store_features"]:
                                response_message += f"• {feature}\n"

                        chat_histories[user_key].append({"role": "assistant", "content": response_message})

                        return {
                            "response": response_message,
                            "chat_title": chat_titles[user_key],
                            "chat_history": chat_histories[user_key]
                        }

            # Check for decline intent (special case)
            elif primary_intent == "no_confirmation":
                # Check if the last assistant message contains a device recommendation prompt
                if user_key in chat_histories and len(chat_histories[user_key]) > 1:
                    last_assistant_message = None
                    for message in reversed(chat_histories[user_key]):
                        if message["role"] == "assistant":
                            last_assistant_message = message["content"]
                            break

                    # Check if the last message contains a device recommendation prompt
                    if last_assistant_message and "would you like to see health monitoring devices from turbomedics" in last_assistant_message.lower():
                        # Handle device recommendation decline
                        chat_histories[user_key].append({"role": "user", "content": "No, I don't need device recommendations right now."})

                        # Use the device confirmation tool
                        confirmation_result = json.loads(confirm_device_recommendation(json.dumps({"confirmed": False})))

                        response_message = confirmation_result.get("message", "")
                        if "alternative_suggestions" in confirmation_result:
                            response_message += "\n\n**Alternative options:**\n"
                            for suggestion in confirmation_result["alternative_suggestions"]:
                                response_message += f"• {suggestion}\n"

                        chat_histories[user_key].append({"role": "assistant", "content": response_message})

                        return {
                            "response": response_message,
                            "chat_title": chat_titles[user_key],
                            "chat_history": chat_histories[user_key]
                        }

                # Continue with health consultation formatting if that was the previous intent
                if previous_intent == "health_consultation":
                        # Format the consultation result in a more conversational, human-like way
                        urgency = consultation_result.get("urgency_level", "Low")
                        urgency_emoji = "🟢" if urgency == "Low" else "🟡" if urgency == "Medium" else "🔴" if urgency == "High" else "⚠️"

                        # Start with a friendly greeting
                        formatted_result = "Hi there! I've looked through your health information and here's what I found.\n\n"

                        # Add urgency level with appropriate wording
                        if urgency == "Low":
                            formatted_result += f"{urgency_emoji} Overall, your health indicators look good! There's no immediate cause for concern based on the data I can see.\n\n"
                        elif urgency == "Medium":
                            formatted_result += f"{urgency_emoji} I've noticed a few things that might need some attention. Nothing urgent, but worth discussing with your doctor in the next few weeks.\n\n"
                        else:
                            formatted_result += f"{urgency_emoji} I've found some concerning indicators that should be addressed soon. I'd recommend speaking with a healthcare provider as soon as possible.\n\n"

                        # Add medical advice in a conversational way
                        medical_advice = consultation_result.get("medical_advice", [])
                        if medical_advice:
                            formatted_result += "Here's my advice based on your health data:\n\n"
                            for i, advice in enumerate(medical_advice):
                                # Remove bullet points and make more conversational
                                advice_text = advice
                                if advice.startswith("- "):
                                    advice_text = advice[2:]
                                formatted_result += f"{advice_text}\n\n"

                        # Add specialist recommendations if any
                        specialist_recommendations = consultation_result.get("specialist_recommendations", [])
                        if specialist_recommendations:
                            if len(specialist_recommendations) == 1:
                                formatted_result += f"Based on what I'm seeing, you might benefit from talking to a {specialist_recommendations[0]}.\n\n"
                            else:
                                formatted_result += "Based on your health data, you might benefit from consulting with these specialists:\n\n"
                                for specialist in specialist_recommendations:
                                    formatted_result += f"• A {specialist}\n"
                                formatted_result += "\n"

                        # Add integrated analysis in a more conversational way
                        integrated_analysis = consultation_result.get("integrated_analysis", [])
                        if integrated_analysis:
                            formatted_result += "Let me break down what I'm seeing in your health data:\n\n"
                            for analysis in integrated_analysis:
                                # Make it more conversational
                                if analysis.startswith("Health score:"):
                                    # Keep the emoji if present
                                    emoji_match = re.search(r'(🩸|🍎|🚶‍♀️|[^\w\s])', analysis)
                                    emoji = emoji_match.group(0) + " " if emoji_match else ""
                                    content = analysis.split(":", 1)[1].strip()
                                    formatted_result += f"{emoji}I notice that {content}\n\n"
                                else:
                                    formatted_result += f"{analysis}\n\n"

                        # Add health summary in a more conversational way
                        health_summary = consultation_result.get("health_summary", {})
                        if health_summary:
                            formatted_result += "Here's a quick summary of your health metrics:\n\n"

                            if "health_score" in health_summary:
                                score_data = health_summary["health_score"]
                                score = score_data.get('score', 'N/A')
                                status = score_data.get('status', 'Unknown')
                                formatted_result += f"Your overall health score is {score}, which is considered '{status}'. "

                            if "kidney_function" in health_summary:
                                kidney_data = health_summary["kidney_function"]
                                kidney_health = kidney_data.get('overall_health', 'Unknown')
                                # Remove emoji if present
                                kidney_health = re.sub(r'[^\w\s]', '', kidney_health).strip()
                                formatted_result += f"Your kidney function appears to be {kidney_health.lower()}. "

                            if "lipid_profile" in health_summary:
                                lipid_data = health_summary["lipid_profile"]
                                cv_risk = lipid_data.get('ascvd_risk', 'Unknown')
                                formatted_result += f"Your cardiovascular risk is in the {cv_risk.lower()} range. "

                            if "liver_function" in health_summary:
                                liver_data = health_summary["liver_function"]
                                liver_risk = liver_data.get('risk_level', 'Unknown')
                                formatted_result += f"Your liver function shows {liver_risk.lower()} risk level."

                            formatted_result += "\n\n"

                        # Save consultation result to user health data
                        user_health_data[user_key]["health_consultation"] = {
                            "result": consultation_result,
                            "timestamp": datetime.now().isoformat()
                        }

                        # Create the final result
                        consultation_result = {
                            "consultation": formatted_result,
                            "doctor_visit_recommended": consultation_result.get("doctor_visit_recommended", False),
                            "urgency_level": urgency
                        }

                        # Check if there was an error
                        if "error" in consultation_result:
                            error_message = f"I'm sorry, there was an error processing your health consultation: {consultation_result['error']}"
                            chat_histories[user_key].append({"role": "assistant", "content": error_message})
                            return {
                                "response": error_message,
                                "chat_title": chat_titles[user_key],
                                "chat_history": chat_histories[user_key]
                            }

                        # Return the consultation result
                        return {
                            "response": consultation_result["consultation"],
                            "chat_title": chat_titles[user_key],
                            "chat_history": chat_histories[user_key],
                            "doctor_visit_recommended": consultation_result["doctor_visit_recommended"],
                            "urgency_level": consultation_result["urgency_level"]
                        }



            # Handle recommendation intent
            if primary_intent == "recommendation":
                # Check if user has any health data
                if user_key in user_health_data and user_health_data[user_key]:
                    # Create a personalized recommendation response
                    recommendation_response = "Based on your health data, here are my personalized recommendations:\n\n"

                    # Add recommendations from health score if available
                    if "health_score" in user_health_data[user_key]:
                        score_data = user_health_data[user_key]["health_score"]
                        if "Improvement Tips" in score_data["result"] and score_data["result"]["Improvement Tips"]:
                            recommendation_response += "**Health Score Recommendations:**\n"
                            tips = score_data["result"]["Improvement Tips"]
                            if isinstance(tips, list):
                                for tip in tips:
                                    if tip.strip():
                                        recommendation_response += f"- {tip.strip()}\n"
                            else:
                                # Handle string format (legacy data)
                                tip_list = tips.split(". ")
                                for tip in tip_list:
                                    if tip.strip():
                                        recommendation_response += f"- {tip.strip()}.\n"
                            recommendation_response += "\n"

                    # Add recommendations from kidney function if available
                    if "kidney_function" in user_health_data[user_key]:
                        kidney_data = user_health_data[user_key]["kidney_function"]
                        if "recommendations" in kidney_data["result"] and kidney_data["result"]["recommendations"]:
                            recommendation_response += "**Kidney Function Recommendations:**\n"

                            # Add kidney disease stage if available
                            kidney_stage = kidney_data["result"].get("kidney_stage", "Unknown")
                            if kidney_stage and kidney_stage != "Unknown":
                                recommendation_response += f"Based on your kidney disease stage ({kidney_stage}), here are my recommendations:\n"

                            for rec in kidney_data["result"]["recommendations"]:
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                    # Add recommendations from lipid profile if available
                    if "lipid_profile" in user_health_data[user_key]:
                        lipid_data = user_health_data[user_key]["lipid_profile"]
                        if "recommendations" in lipid_data["result"] and lipid_data["result"]["recommendations"]:
                            recommendation_response += "**Cardiac Health Recommendations:**\n"
                            for rec in lipid_data["result"]["recommendations"][:5]:  # Limit to 5 recommendations
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                            # Add diet plan information if available
                            if "diet_plan" in lipid_data["result"] and lipid_data["result"]["diet_plan"]:
                                diet_plan = lipid_data["result"]["diet_plan"]
                                recommendation_response += "**Heart-Healthy Diet Recommendations:**\n"
                                for food in diet_plan.get("recommended_foods", [])[:3]:  # Limit to 3 foods
                                    recommendation_response += f"- {food}\n"
                                recommendation_response += "\n"

                    # Add recommendations from liver function if available
                    if "liver_function" in user_health_data[user_key]:
                        liver_data = user_health_data[user_key]["liver_function"]
                        if "recommendations" in liver_data["result"] and liver_data["result"]["recommendations"]:
                            recommendation_response += "**Liver Health Recommendations:**\n"
                            for rec in liver_data["result"]["recommendations"][:5]:  # Limit to 5 recommendations
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                    # Add alerts from vital signs if available
                    if "vital_signs" in user_health_data[user_key]:
                        vital_data = user_health_data[user_key]["vital_signs"]
                        if "alerts" in vital_data["result"] and vital_data["result"]["alerts"] and vital_data["result"]["alerts"] != "No abnormal patterns detected.":
                            recommendation_response += "**Vital Signs Recommendations:**\n"
                            alerts = vital_data["result"]["alerts"].split("\n")
                            for alert in alerts:
                                if alert.strip():
                                    recommendation_response += f"- {alert.strip()}\n"
                            recommendation_response += "\n"

                    # Add priority recommendations based on health score
                    if "health_score" in user_health_data[user_key]:
                        score_data = user_health_data[user_key]["health_score"]
                        score = score_data["result"].get("Total Score", 0)

                        # Add a personalized priority recommendation
                        recommendation_response += "**Priority Recommendation:**\n"
                        if score < 50:
                            recommendation_response += "- 🚨 Your health score indicates several areas needing attention. I recommend scheduling a comprehensive check-up with your healthcare provider soon.\n\n"
                        elif score < 70:
                            recommendation_response += "- ⚠️ Focus on the specific areas mentioned above to improve your overall health score. Small consistent changes can make a big difference.\n\n"
                        else:
                            recommendation_response += "- ✅ You're doing well! Continue your healthy habits and consider the recommendations above for further improvement.\n\n"

                    # Override the model response with our personalized recommendations
                    model_response = recommendation_response
                    tools_used.append("personalized_recommendations")
                else:
                    # If no health data, prompt user to enter some
                    tool_response += "\n\nI don't have any health data for you yet. Would you like to enter your health data for personalized recommendations? You can choose from:\n\n- Health Score Analysis\n- Vital Signs Monitoring\n- Kidney Function Test\n- Lipid Profile Test\n- Liver Function Test\n\nType the name of the test you'd like to perform."
                    tools_used.append("no_health_data")

            # Health score analysis intent
            elif primary_intent == "health_score":
                tool_response += "\n\nWould you like to analyze your health score? Type 'yes' to begin."
                tools_used.append("health_score_intent")

            # Vital signs monitoring intent
            elif primary_intent == "vital_signs":
                tool_response += "\n\nWould you like to enter your vital signs for monitoring? Type 'yes' to begin."
                tools_used.append("vital_signs_intent")

            # Kidney function test intent
            elif primary_intent == "kidney_function":
                tool_response += "\n\nWould you like to analyze your kidney function? Type 'yes' to begin."
                tools_used.append("kidney_function_intent")

            # Lipid profile test intent
            elif primary_intent == "lipid_profile":
                tool_response += "\n\nWould you like to analyze your lipid profile for cardiac health planning? This will include ASCVD risk assessment, personalized diet recommendations, and a comprehensive cardiac health plan. Type 'yes' to begin."
                tools_used.append("lipid_profile_intent")

            # Liver function test intent
            elif primary_intent == "liver_function":
                tool_response += "\n\nWould you like to analyze your liver function? This will include liver enzyme analysis, hepatic health assessment, and personalized recommendations. Type 'yes' to begin."
                tools_used.append("liver_function_intent")

            # Health consultation intent
            elif primary_intent == "health_consultation":
                # Check if user has any health data for a comprehensive consultation
                if user_key in user_health_data and user_health_data[user_key]:
                    tool_response += "\n\nI'd be happy to provide a comprehensive health consultation based on your data. This will include an analysis of your health metrics, personalized recommendations, specialist referrals if needed, recommended appointments, tests, follow-up schedules, and proactive care coordination. Would you like me to do that now? Just type 'yes' and I'll get started."
                    tools_used.append("consultation_intent")
                else:
                    tool_response += "\n\nI don't have any of your health information yet. I'd need some basic health data to give you a proper consultation. Would you like to enter some health information now? You could start with:\n\n• A quick Health Score Analysis\n• Your Vital Signs\n• Kidney Function Test results\n• Lipid Profile Test results\n• Liver Function Test results\n\nJust let me know which one you'd like to start with."
                    tools_used.append("no_health_data")


            # Health trends intent (new)
            elif primary_intent == "health_trends":
                # Handle health trends analysis
                trend_analysis = analyze_health_trends(user_key)
                if trend_analysis:
                    model_response = trend_analysis
                    tools_used.append("health_trends")
                else:
                    tool_response += "\n\nI don't have enough historical health data to analyze trends yet. Would you like to enter your current health data to start tracking?"
                    tools_used.append("no_trend_data")

            # Device recommendations intent
            elif primary_intent == "device_recommendations":
                # Check if user has any health data for device recommendations
                if user_key in user_health_data and user_health_data[user_key]:
                    tool_response += "\n\nWould you like me to recommend health devices based on your health data? These connected devices can help you monitor your vitals at home. Type 'yes' to see device recommendations."
                    tools_used.append("device_recommendations_intent")
                else:
                    tool_response += "\n\nI don't have any of your health information yet. I'd need some basic health data to recommend appropriate devices. Would you like to enter some health information now? You could start with a quick Health Score Analysis or your Vital Signs."
                    tools_used.append("no_health_data")

            # Chronic tracker intent
            elif primary_intent == "chronic_tracker":
                # Ask which chronic condition they want to track
                tool_response += "\n\nI'd be happy to help you track your chronic condition. Would you like to track diabetes, hypertension, asthma, heart disease, or kidney disease? Type 'yes' to begin."
                tools_used.append("chronic_tracker_intent")

        # Add tool response to model response if any
        if tool_response:
            model_response += tool_response

        # Add response to chat history
        chat_histories[user_key].append({"role": "assistant", "content": model_response})

        # Check if health consultation intent was detected
        response_data = {
            "response": model_response,
            "chat_title": chat_titles[user_key],
            "chat_history": chat_histories[user_key],
            "tools_used": tools_used
        }

        # Add quick action for health consultation if that intent was detected
        if "consultation_intent" in tools_used:
            response_data["show_health_consultation"] = True
            response_data["health_consultation_link"] = f"/health-consultation?user_id={user_key[0]}_{user_key[1]}"

        return response_data
    except Exception as e:
        error_msg = f"Failed to process agent query: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

# === ENDPOINTS ===
@app.get("/")
async def root():
    return {"message": "Integrated Health Agent API is running"}

@app.get("/health")
async def health_check():
    """Simple health check endpoint for monitoring"""
    return {"status": "healthy"}

@app.get("/default-health-data")
async def default_health_data():
    """Get default health data"""
    try:
        # Use the exact DEFAULT_HEALTH_DATA structure as specified
        default_data = {
            "Glucose": None,
            "SpO2": None,
            "ECG (Heart Rate)": None,
            "Blood Pressure (Systolic)": None,
            "Blood Pressure (Diastolic)": None,
            "Weight (BMI)": None,  # This field stores the BMI value, not the weight
            "Temperature": None,
            "Malaria": "Unknown",
            "Widal Test": "Unknown",
            "Hepatitis B": "Unknown",
            "Voluntary Serology": "Unknown",
            "Perfusion_index": None,
            "Waist Circumference": None,
            "Fev": None
        }

        logging.info(f"Returning default health data: {json.dumps(default_data)}")
        return default_data
    except Exception as e:
        error_msg = f"Error getting default health data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.get("/user-health-data/{user_id}")
async def get_user_health_data(user_id: str, data_type: Optional[str] = None):
    """Get stored health data for a specific user

    Args:
        user_id: The ID of the user
        data_type: Optional type of health data to retrieve (vital_signs, health_score, kidney_function, lipid_profile)
                  If not provided, returns all health data for the user
    """
    try:
        if user_id not in user_health_data:
            return {"message": "No health data found for this user"}

        if data_type:
            if data_type not in user_health_data[user_id]:
                return {"message": f"No {data_type} data found for this user"}
            return user_health_data[user_id][data_type]

        return user_health_data[user_id]
    except Exception as e:
        error_msg = f"Error retrieving user health data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.get("/status")
async def status():
    """Check if the server is running and models are loaded"""
    models_status = {}
    for model_name in [QWEN_MODEL, DEEPSEEK_MODEL]:
        models_status[model_name] = {
            "vector_index": model_name in vector_indexes,
            "metadata": model_name in vector_docs,
            "embeddings": model_name in embedding_models
        }

    return {
        "status": "running",
        "models": models_status
    }

# This endpoint is already defined above, so we're removing the duplicate

from fastapi import Query

@app.get("/chat-history")
async def get_chat_history(user_id: str = Query(...)):
    """Return chat history and health data for the user"""
    try:
        # Get messages for this user
        messages = chat_histories.get(user_id, [])

        # Get health data (vital signs, health score, etc.)
        health_data = user_health_data.get(user_id, {})

        # Get chat title if available
        title = chat_titles.get(user_id, "Health Chat")

        return {
            "messages": messages,
            "health_data": health_data,
            "chat_title": title
        }
    except Exception as e:
        error_msg = f"Error fetching chat history: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}


@app.post("/query")
async def get_response(chat: ChatRequest):
    """Handle chat queries through the agent"""
    try:
        user_id, session_id, query, model = chat.user_id, chat.session_id, chat.query, chat.model

        # Validate model selection
        if model not in [QWEN_MODEL, DEEPSEEK_MODEL]:
            return {"error": f"Invalid model selection. Choose either {QWEN_MODEL} or {DEEPSEEK_MODEL}."}

        # Process the query through the agent
        result = process_agent_query(query, user_id, session_id, model)

        return result
    except Exception as e:
        error_msg = f"Error processing query: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/track-progress")
async def track_vital_progress(request: ProgressTrackRequest):
    try:
        user_id = request.user_id
        data = request.vital_signs
        timestamp = datetime.now().isoformat()

        if user_id not in user_health_data:
            user_health_data[user_id] = []

        user_health_data[user_id].append({**data, "timestamp": timestamp})
        logging.info(f"Updated progress history for user: {user_id}")

        summary = generate_monthly_summary(user_id, user_health_data)
        recommendations = generate_trend_recommendations(summary.get("trend_analysis", {}))

        return {
            "active_vitals": data,
            "monthly_summary": summary,
            "recommendations": recommendations,
            "raw_data_points": len(user_health_data[user_id])
        }

    except Exception as e:
        logging.error(f"Error tracking vitals for {request.user_id}: {str(e)}")
        return {"error": str(e)}

@app.post("/vital-signs")
async def process_vital_signs_endpoint(request: VitalSignsRequest):
    """Process vital signs data"""
    try:
        user_id = request.user_id
        user_key = convert_user_id_to_key(user_id)
        vital_signs = request.vital_signs

        # Process the vital signs
        result = process_vital_signs(vital_signs)

        # Save vital signs data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["vital_signs"] = {
            "data": vital_signs,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved vital signs data for user {user_key}")

        # Update chat history if user exists
        if user_key in chat_histories:
            vital_signs_str = ", ".join([f"{k}: {v}" for k, v in vital_signs.items()])
            chat_histories[user_key].append({"role": "user", "content": f"My vital signs are: {vital_signs_str}"})

            response_content = f"I've analyzed your vital signs.\n\n{result.get('analysis', '')}"
            if result.get('alerts'):
                response_content += f"\n\nAlerts: {result['alerts']}"

            chat_histories[user_key].append({"role": "assistant", "content": response_content})

        return result
    except Exception as e:
        error_msg = f"Error processing vital signs: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

# Health score endpoint removed - using realtime-health-score instead

@app.post("/kidney-function")
async def analyze_kidney_function_endpoint(request: KidneyFunctionRequest):
    """Analyze kidney function data using the updated tool"""
    try:
        user_id = request.user_id
        kidney_data = request.kidney_data

        if not kidney_data:
            error_msg = "No kidney data provided"
            logging.error(error_msg)
            return {"error": error_msg}

        # Run analysis tool - the updated tool returns a comprehensive result
        result = kidney_function_analysis_tool(kidney_data)

        # Save kidney data to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["kidney_function"] = {
            "data": kidney_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved kidney function data for user {user_id}")

        # Format for chat memory (optional)
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": "Please analyze my kidney function."})

            # Use the doctor-like summary if available, otherwise format a standard summary
            if "doctor_summary" in result and result["doctor_summary"]:
                doctor_summary = result.get("doctor_summary", "")
                overall_health = result.get('overall_health', 'Unknown')
                kidney_stage = result.get('kidney_stage', 'Unknown')

                summary = f"**Kidney Function Analysis**\n\n"
                summary += f"{doctor_summary}\n\n"

                # Add kidney disease stage if available
                if kidney_stage and kidney_stage != "Unknown":
                    summary += f"**Kidney Disease Stage:** {kidney_stage}\n\n"

                summary += f"**Detailed findings:**\n{overall_health}\n"

                # Add analysis results
                analysis_items = result.get("analysis", [])
                if analysis_items:
                    summary += "\n**Parameter Analysis:**\n"
                    # Only include the most important parameters in the chat summary
                    key_params = ["BUN", "Serum Creatinine", "eGFR", "ACR"]
                    for item in analysis_items:
                        for param in key_params:
                            if item.startswith(f"{param}:") or item.startswith(f"{param} Detail:"):
                                summary += f"- {item}\n"
                    summary += "\n"

                # Add recommendations if available
                if result.get("recommendations"):
                    summary += "\n**Renal Health Management Recommendations:**\n"
                    for rec in result["recommendations"]:
                        summary += f"- {rec}\n"
            else:
                # Fallback to the original formatting
                analysis_items = result.get("analysis", [])
                formatted_analysis = "\n".join([f"- {item}" for item in analysis_items]) if isinstance(analysis_items, list) else analysis_items

                summary = f"🧪 Kidney Function Analysis\n\n"
                if formatted_analysis:
                    summary += f"{formatted_analysis}\n\n"

                summary += f"🩺 Overall Health: {result.get('overall_health', 'Unknown')}\n"

                # Add kidney disease stage if available
                kidney_stage = result.get('kidney_stage', 'Unknown')
                if kidney_stage and kidney_stage != "Unknown":
                    summary += f"🏥 Kidney Disease Stage: {kidney_stage}\n"

                summary += f"📊 Confidence Level: {result.get('confidence_level', 'Unknown')}"

                if result.get("missing_parameters"):
                    summary += f"\n🔍 Missing Parameters: {', '.join(result['missing_parameters'])}"

                # Add recommendations if available
                if result.get("recommendations"):
                    summary += "\n\n🔸 Renal Health Management Recommendations:\n"
                    for rec in result["recommendations"]:
                        summary += f"- {rec}\n"

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_id].append({"role": "assistant", "content": summary})

        return result

    except Exception as e:
        error_msg = f"Error analyzing kidney function: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/health-consultation")
async def health_consultation_endpoint_post(request: UserIDRequest, treat_unknown_as_null: bool = Query(False)):
    """Perform a comprehensive health consultation using all available health data (POST method)"""
    return await health_consultation_endpoint(request.user_id, treat_unknown_as_null)

@app.get("/health-consultation")
async def health_consultation_endpoint_get(user_id: str = Query(...), treat_unknown_as_null: bool = Query(False)):
    """Perform a comprehensive health consultation using all available health data (GET method)"""
    return await health_consultation_endpoint(user_id, treat_unknown_as_null)

async def health_consultation_endpoint(user_id: str, treat_unknown_as_null: bool = False):
    """Perform a comprehensive health consultation using all available health data"""
    try:

        # Check if user has health data
        if user_id not in user_health_data or not user_health_data[user_id]:
            return {
                "error": "No health data available for this user",
                "message": "Please complete at least one health assessment before requesting a consultation."
            }

        # Process health data to handle "Unknown" values if requested
        processed_health_data = {}
        if treat_unknown_as_null:
            for category, data in user_health_data[user_id].items():
                processed_health_data[category] = data.copy()

                # Process data fields if they exist
                if "data" in data and isinstance(data["data"], dict):
                    processed_data = {}
                    for key, value in data["data"].items():
                        if value != "Unknown" and value is not None and value != "" and value != "null":
                            processed_data[key] = value
                    processed_health_data[category]["data"] = processed_data

                # Process result fields if they exist
                if "result" in data and isinstance(data["result"], dict):
                    for key, value in data["result"].items():
                        if value == "Unknown":
                            data["result"][key] = None
        else:
            processed_health_data = user_health_data[user_id]

        # Prepare input for the consultation tool
        consultation_input = {
            "user_id": user_id,
            "health_data": processed_health_data
        }

        # Run the comprehensive health consultation tool
        consultation_result = json.loads(automated_health_consultation_tool.func(json.dumps(consultation_input)))

        # Save consultation result to user health data
        user_health_data[user_id]["health_consultation"] = {
            "result": consultation_result,
            "timestamp": datetime.now().isoformat()
        }

        # Format the consultation result in a more conversational, human-like way
        urgency = consultation_result.get("urgency_level", "Low")
        urgency_emoji = "🟢" if urgency == "Low" else "🟡" if urgency == "Medium" else "🔴" if urgency == "High" else "⚠️"

        # Start with a friendly greeting
        formatted_result = "Hi there! I've looked through your health information and here's what I found.\n\n"

        # Add urgency level with appropriate wording
        if urgency == "Low":
            formatted_result += f"{urgency_emoji} Overall, your health indicators look good! There's no immediate cause for concern based on the data I can see.\n\n"
        elif urgency == "Medium":
            formatted_result += f"{urgency_emoji} I've noticed a few things that might need some attention. Nothing urgent, but worth discussing with your doctor in the next few weeks.\n\n"
        else:
            formatted_result += f"{urgency_emoji} I've found some concerning indicators that should be addressed soon. I'd recommend speaking with a healthcare provider as soon as possible.\n\n"

        # Add medical advice in a conversational way
        medical_advice = consultation_result.get("medical_advice", [])
        if medical_advice:
            formatted_result += "Here's my advice based on your health data:\n\n"
            for advice in medical_advice:
                # Remove bullet points and make more conversational
                advice_text = advice
                if advice.startswith("- "):
                    advice_text = advice[2:]
                formatted_result += f"{advice_text}\n\n"

        # Add specialist recommendations if any
        specialist_recommendations = consultation_result.get("specialist_recommendations", [])
        if specialist_recommendations:
            if len(specialist_recommendations) == 1:
                formatted_result += f"Based on what I'm seeing, you might benefit from talking to a {specialist_recommendations[0]}.\n\n"
            else:
                formatted_result += "Based on your health data, you might benefit from consulting with these specialists:\n\n"
                for specialist in specialist_recommendations:
                    formatted_result += f"• A {specialist}\n"
                formatted_result += "\n"

        # Add appointment recommendations from appointment intelligence
        appointment_recommendations = consultation_result.get("appointment_recommendations", [])
        appointment_reasons = consultation_result.get("appointment_reasons", {})
        appointment_urgency_levels = consultation_result.get("appointment_urgency_levels", {})

        if appointment_recommendations:
            formatted_result += "**Recommended Appointments:**\n\n"
            for appointment in appointment_recommendations:
                reason = appointment_reasons.get(appointment, "General health monitoring")
                urgency = appointment_urgency_levels.get(appointment, "Low")
                urgency_icon = "🔴" if urgency == "High" else "🟡" if urgency == "Medium" else "🟢"

                formatted_result += f"{urgency_icon} **{appointment}**: {reason}\n"
            formatted_result += "\n"

        # Add recommended tests from appointment intelligence
        recommended_tests = consultation_result.get("recommended_tests", [])
        if recommended_tests:
            formatted_result += "**Recommended Tests and Screenings:**\n\n"
            for test in recommended_tests:
                formatted_result += f"• {test}\n"
            formatted_result += "\n"

        # Add follow-up schedule from appointment intelligence
        follow_up_schedule = consultation_result.get("follow_up_schedule", {})
        if follow_up_schedule:
            formatted_result += "**Follow-up Schedule:**\n\n"
            for check, timeframe in follow_up_schedule.items():
                formatted_result += f"• {check}: {timeframe}\n"
            formatted_result += "\n"

        # Add care coordination recommendations from appointment intelligence
        care_coordination = consultation_result.get("care_coordination", [])
        if care_coordination:
            formatted_result += "**Proactive Care Coordination:**\n\n"
            for care in care_coordination:
                formatted_result += f"• {care}\n"
            formatted_result += "\n"

        # Add integrated analysis in a more conversational way
        integrated_analysis = consultation_result.get("integrated_analysis", [])
        if integrated_analysis:
            formatted_result += "Let me break down what I'm seeing in your health data:\n\n"
            for analysis in integrated_analysis:
                # Make it more conversational
                if analysis.startswith("Health score:"):
                    # Keep the emoji if present
                    emoji_match = re.search(r'(🩸|🍎|🚶‍♀️|[^\w\s])', analysis)
                    emoji = emoji_match.group(0) + " " if emoji_match else ""
                    content = analysis.split(":", 1)[1].strip()
                    formatted_result += f"{emoji}I notice that {content}\n\n"
                else:
                    formatted_result += f"{analysis}\n\n"

        # Add health summary in a more conversational way
        health_summary = consultation_result.get("health_summary", {})
        if health_summary:
            formatted_result += "Here's a quick summary of your health metrics:\n\n"

            if "health_score" in health_summary:
                score_data = health_summary["health_score"]
                score = score_data.get('score', 'N/A')
                status = score_data.get('status', 'Unknown')
                if status != "Unknown":
                    formatted_result += f"Your overall health score is {score}, which is considered '{status}'. "
                else:
                    formatted_result += f"Your overall health score could not be determined due to insufficient data. "

            if "kidney_function" in health_summary:
                kidney_data = health_summary["kidney_function"]
                kidney_health = kidney_data.get('overall_health', 'Unknown')
                kidney_stage = kidney_data.get('kidney_stage', 'Unknown')

                if kidney_health != "Unknown":
                    # Remove emoji if present
                    kidney_health = re.sub(r'[^\w\s]', '', kidney_health).strip()
                    formatted_result += f"Your kidney function appears to be {kidney_health.lower()}. "
                    if kidney_stage and kidney_stage != "Unknown":
                        formatted_result += f"You are currently at {kidney_stage}. "
                else:
                    formatted_result += f"Your kidney function could not be determined due to insufficient data. "

            if "lipid_profile" in health_summary:
                lipid_data = health_summary["lipid_profile"]
                cv_risk = lipid_data.get('ascvd_risk', 'Unknown')

                if cv_risk != "Unknown":
                    formatted_result += f"Your cardiovascular risk is in the {cv_risk.lower()} range. "

                    # Add diet plan information if available
                    if "diet_plan" in lipid_data:
                        formatted_result += f"I've included a heart-healthy diet plan in your cardiac health analysis. "

                    # Add cardiac health plan information if available
                    if "cardiac_health_plan" in lipid_data:
                        formatted_result += f"Your cardiac health plan includes specific monitoring recommendations and follow-up guidance."
                else:
                    formatted_result += f"Your cardiovascular risk could not be determined due to insufficient data. "

            formatted_result += "\n\n"

        # Add to chat history
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": "I'd like a health consultation based on my data."})
            chat_histories[user_id].append({"role": "assistant", "content": formatted_result})

        return {
            "consultation": formatted_result,
            "doctor_visit_recommended": consultation_result.get("doctor_visit_recommended", False),
            "urgency_level": urgency,
            "appointment_recommendations": consultation_result.get("appointment_recommendations", []),
            "recommended_tests": consultation_result.get("recommended_tests", []),
            "follow_up_schedule": consultation_result.get("follow_up_schedule", {}),
            "care_coordination": consultation_result.get("care_coordination", [])
        }

    except Exception as e:
        error_msg = f"Error performing health consultation: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/lipid-profile")
async def analyze_lipid_profile_endpoint(request: LipidProfileRequest):
    """Analyze lipid profile data"""
    try:
        user_id = request.user_id
        lipid_data = request.lipid_data

        if not lipid_data:
            return {"error": "No lipid data provided."}

        result = analyze_lipid_profile(lipid_data)

        # Save lipid data to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["lipid_profile"] = {
            "data": lipid_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved lipid profile data for user {user_id}")

        # Add formatted text for chat history (if integrated)
        if user_id in chat_histories:
            chat_histories[user_id].append({
                "role": "user",
                "content": "Please analyze my lipid profile."
            })

            # Create a comprehensive summary with all the new features
            summary = f"**Cardiac Health Analysis**\n\n"

            # Add doctor's summary if available
            if "doctor_summary" in result and result["doctor_summary"]:
                summary += f"{result['doctor_summary']}\n\n"

            # Add classification results
            summary += "**Here's a breakdown of your results:**\n"
            for component, level in result["classification"].items():
                component_name = component.replace('_', ' ').title()

                # Add emoji indicators
                if component == 'hdl':  # For HDL, high is good
                    emoji = "✅" if level in ['optimal', 'high'] else "⚠️" if level == 'borderline' else "❗"
                else:  # For all other components, low is generally good
                    emoji = "✅" if level == 'optimal' else "⚠️" if level in ['borderline', 'near optimal'] else "❗"

                summary += f"{emoji} **{component_name}**: {level.title()}\n"

            # Add ASCVD risk
            risk = result.get('ascvd_risk', 'Unknown')
            summary += f"\n**Cardiovascular Risk Assessment**: {risk} "
            if "low" in risk.lower():
                summary += "✅"
                risk_explanation = "This means your risk of developing cardiovascular disease in the next 10 years is relatively low based on your lipid values."
            elif "borderline" in risk.lower():
                summary += "⚠️"
                risk_explanation = "This suggests you have some risk factors that could increase your chances of developing cardiovascular disease in the next 10 years."
            elif "intermediate" in risk.lower() or "moderate" in risk.lower():
                summary += "⚠️"
                risk_explanation = "This suggests you have several risk factors that could increase your chances of developing cardiovascular disease in the next 10 years."
            elif "high" in risk.lower():
                summary += "❗"
                risk_explanation = "This indicates a higher probability of developing cardiovascular disease in the next 10 years, and we should take proactive steps to address this."
            else:
                risk_explanation = "Please consult with your healthcare provider to understand your cardiovascular risk better."

            summary += f"\n{risk_explanation}\n"

            # Add detailed parameter explanations
            parameter_explanations = result.get("parameter_explanations", [])
            if parameter_explanations:
                summary += "\n**Understanding Your Lipid Profile:**\n"
                for param in parameter_explanations[:3]:  # Limit to 3 to avoid overly long messages
                    summary += f"- **{param['parameter']}** ({param['value']} mg/dL): {param['explanation']} {param['status_explanation']}\n"

            # Add recommendations
            recommendations = result.get("recommendations", [])
            if recommendations:
                summary += "\n**Here's what I recommend:**\n"
                for rec in recommendations[:5]:  # Limit to 5 recommendations
                    summary += f"- {rec}\n"

            # Add diet plan highlights
            diet_plan = result.get("diet_plan", {})
            if diet_plan:
                summary += f"\n**{diet_plan.get('title', 'Heart-Healthy Diet Plan')}**\n"
                summary += f"{diet_plan.get('overview', '')}\n\n"

                # Add a few recommended foods
                recommended_foods = diet_plan.get('recommended_foods', [])
                if recommended_foods:
                    summary += "**Key Recommended Foods:**\n"
                    for food in recommended_foods[:3]:  # Limit to 3 items
                        summary += f"- {food}\n"

            # Add cardiac health plan highlights
            cardiac_health_plan = result.get("cardiac_health_plan", {})
            if cardiac_health_plan:
                summary += f"\n**{cardiac_health_plan.get('title', 'Cardiac Health Plan')}**\n"
                summary += f"{cardiac_health_plan.get('overview', '')}\n\n"

                if cardiac_health_plan.get('follow_up'):
                    summary += f"**Follow-up Plan:** {cardiac_health_plan.get('follow_up')}\n"

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error analyzing lipid profile: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}

@app.post("/lung-capacity")
async def analyze_lung_capacity_endpoint(request: LungCapacityRequest):
    """Analyze lung capacity and respiratory health"""
    try:
        user_id = request.user_id
        spirometry_data = request.spirometry_data

        if not spirometry_data:
            return {"error": "No spirometry data provided."}

        result = process_lung_capacity(spirometry_data)

        # Save lung capacity data to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["lung_capacity"] = {
            "data": spirometry_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved lung capacity data for user {user_id}")

        # Add formatted text for chat history (if integrated)
        if user_id in chat_histories:
            chat_histories[user_id].append({
                "role": "user",
                "content": "Please analyze my lung capacity and respiratory health."
            })

            # Use the doctor-like summary if available, otherwise format a standard summary
            if "doctor_summary" in result and result["doctor_summary"]:
                doctor_summary = result.get("doctor_summary", "")
                recommendations = "\n- ".join(result.get("recommendations", ["No specific recommendations."]))

                summary = f"**Lung Capacity Analysis**\n\n"
                summary += f"{doctor_summary}\n\n"
                summary += f"**Here's what I recommend:**\n- {recommendations}"
            else:
                # Fallback to the original formatting
                analysis_text = "\n".join([f"- {item}" for item in result.get("analysis", [])])
                risk_level = result.get("respiratory_risk_level", "Unknown")
                conditions = ", ".join(result.get("potential_conditions", ["None identified"]))
                recommendations = "\n- ".join(result.get("recommendations", ["No specific recommendations."]))

                summary = f"**Lung Capacity Analysis**\n\n"
                summary += f"**Analysis:**\n{analysis_text}\n\n"
                summary += f"**Respiratory Risk Level:** {risk_level}\n\n"
                summary += f"**Potential Conditions:** {conditions}\n\n"
                summary += f"**Recommendations:**\n- {recommendations}"

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error analyzing lung capacity: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}


@app.post("/test-results")
async def analyze_test_results_endpoint(request: TestResultsRequest):
    """Analyze malaria and widal test results"""
    try:
        user_id = request.user_id
        test_results = request.test_results

        if not test_results:
            return {"error": "No test results provided."}

        result = process_test_results(test_results)

        # Save test results data to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["test_results"] = {
            "data": test_results,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved test results data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Create a summary of the test results for the chat history
            test_types = test_results.get("selected_tests", [])
            test_types_str = ", ".join(test_types)

            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": f"I've submitted my {test_types_str} results for analysis."
            })

            # Create a summary of the analysis for the assistant's response
            summary = f"I've analyzed your {test_types_str} results.\n\n"

            # Add doctor summary if available
            if "doctor_summary" in result:
                summary += f"{result['doctor_summary']}\n\n"

            # Add urgency level with appropriate emoji
            urgency_level = result.get("urgency_level", "Low")
            if urgency_level == "High":
                summary += "**Urgency Level: 🚨 High**\n\n"
            elif urgency_level == "Medium":
                summary += "**Urgency Level: ⚠️ Medium**\n\n"
            else:
                summary += "**Urgency Level: ✅ Low**\n\n"

            # Add recommendations
            if "recommendations" in result and result["recommendations"]:
                summary += "**Recommendations:**\n"
                for rec in result["recommendations"]:
                    summary += f"{rec}\n"

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error analyzing test results: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}




@app.post("/realtime-health-score")
async def realtime_health_score_endpoint(request: RealTimeHealthScoreRequest):
    """Generate a real-time health score that combines vitals, lifestyle, and test results"""
    try:
        user_id = request.user_id
        health_data = request.health_data

        if not health_data:
            return {"error": "No health data provided."}

        # Process health data to handle "Unknown" values
        processed_health_data = {}
        for key, value in health_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle test results with string values
            elif key in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]:
                processed_health_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    processed_health_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_health_data[key] = value

        # Process the real-time health score with processed data
        result = process_realtime_health_score(processed_health_data)

        # Save real-time health score data to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["realtime_health_score"] = {
            "data": processed_health_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved real-time health score data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": "I'd like to see my real-time health score."
            })

            # Create a summary of the analysis for the assistant's response
            summary = "I've analyzed your comprehensive health data and generated a real-time health score.\n\n"

            # Add total score and health status
            summary += f"**Total Health Score: {result.get('Total Score', 0)}** - {result.get('Health Status', 'Unknown')}\n\n"

            # Add category scores
            if "Category Scores" in result:
                summary += "**Category Breakdown:**\n"
                for category, score in result["Category Scores"].items():
                    summary += f"- {category}: {score}\n"
                summary += "\n"

            # Add areas needing improvement
            vitals_issues = result.get("Vitals Needing Improvement", [])
            lifestyle_issues = result.get("Lifestyle Factors Needing Improvement", [])
            test_issues = result.get("Test Results Needing Improvement", [])

            if vitals_issues or lifestyle_issues or test_issues:
                summary += "**Areas Needing Improvement:**\n"

                if vitals_issues:
                    summary += "Vitals:\n"
                    for issue in vitals_issues:
                        summary += f"- {issue}\n"

                if lifestyle_issues:
                    summary += "Lifestyle Factors:\n"
                    for issue in lifestyle_issues:
                        summary += f"- {issue}\n"

                if test_issues:
                    summary += "Test Results:\n"
                    for issue in test_issues:
                        summary += f"- {issue}\n"

                summary += "\n"

            # Add improvement tips
            if "Improvement Tips" in result and result["Improvement Tips"]:
                summary += "**Personalized Recommendations:**\n"
                for tip in result["Improvement Tips"]:
                    summary += f"{tip}\n"

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error generating real-time health score: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}

@app.post("/device-recommendations")
async def device_recommendations_endpoint(request: DeviceRecommendationRequest):
    """Suggest connected health devices based on missing or abnormal vitals with test-specific recommendations"""
    try:
        user_id = request.user_id
        health_data = request.health_data
        test_type = request.test_type if hasattr(request, 'test_type') else None
        test_data = request.test_data if hasattr(request, 'test_data') else None

        # If test_data is provided, use it as the primary health data
        if test_data:
            health_data = test_data
        # If no health data provided, use stored data if available
        elif not health_data and user_id in user_health_data:
            # Try to get the most recent health data based on test_type if provided
            if test_type and test_type in user_health_data[user_id]:
                health_data = user_health_data[user_id][test_type]["data"]
            # Otherwise fall back to general health data
            elif "health_score" in user_health_data[user_id]:
                health_data = user_health_data[user_id]["health_score"]["data"]
            elif "realtime_health_score" in user_health_data[user_id]:
                health_data = user_health_data[user_id]["realtime_health_score"]["data"]
            else:
                # Use default health data structure if no data is available
                health_data = get_default_health_data()["data"]

        if not health_data:
            return {"error": "No health data provided or found in user records."}

        # Process health data to handle "Unknown" values
        processed_health_data = {}
        for key, value in health_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle test results with string values
            elif key in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]:
                processed_health_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    processed_health_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_health_data[key] = value

        # Format data for the tool, including test_type if available
        tool_input = {
            "data": processed_health_data
        }
        if test_type:
            tool_input["test_type"] = test_type

        health_data_json = json.dumps(tool_input)

        # Use the device recommender tool
        result = json.loads(recommend_health_devices(health_data_json))

        # Add test_type to the result
        if test_type:
            result["test_type"] = test_type

        # Save device recommendations to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["device_recommendations"] = {
            "data": processed_health_data,
            "test_type": test_type,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved device recommendations for user {user_id} with test_type: {test_type}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Create a test-specific message
            if test_type:
                test_type_name = {
                    "vital_signs": "vital signs",
                    "kidney_function": "kidney function",
                    "lipid_profile": "lipid profile",
                    "lung_capacity": "lung capacity",
                    "test_results": "test results",
                    "realtime_health_score": "health score"
                }.get(test_type, "health")

                user_message = f"Can you recommend health devices that would help me monitor my {test_type_name}?"
            else:
                user_message = "Can you recommend health devices that would help me monitor my health?"

            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": user_message
            })

            # Create a test-specific title for the response
            if test_type:
                test_type_name = {
                    "vital_signs": "Vital Signs",
                    "kidney_function": "Kidney Function",
                    "lipid_profile": "Cardiovascular Health",
                    "lung_capacity": "Respiratory Health",
                    "test_results": "Test Results",
                    "realtime_health_score": "Health Score"
                }.get(test_type, "Health")

                summary = f"Based on your {test_type_name.lower()} data, here are my device recommendations for monitoring your {test_type_name.lower()}:\n\n"
            else:
                summary = "Based on your health data, here are my device recommendations:\n\n"

            # Add the recommendation summary
            if "recommendation_summary" in result:
                summary += result["recommendation_summary"]
            else:
                # Fallback to a more basic summary if the detailed one isn't available
                missing_vitals = result.get("missing_vitals", [])
                abnormal_vitals = result.get("abnormal_vitals", [])

                if missing_vitals:
                    summary += "**Recommended measurements to track:**\n"
                    for vital in missing_vitals:
                        summary += f"- {vital}\n"
                    summary += "\n"

                if abnormal_vitals:
                    summary += "**Key metrics to monitor closely:**\n"
                    for vital in abnormal_vitals:
                        summary += f"- {vital}\n"
                    summary += "\n"

                # Add test-specific device recommendations if available
                test_specific_devices = result.get("test_specific_devices", [])
                if test_type and test_specific_devices:
                    test_type_name = {
                        "vital_signs": "Vital Signs",
                        "kidney_function": "Kidney Function",
                        "lipid_profile": "Cardiovascular Health",
                        "lung_capacity": "Respiratory Health",
                        "test_results": "Test Results",
                        "realtime_health_score": "Health Score"
                    }.get(test_type, "Health")

                    summary += f"**Recommended devices for {test_type_name} monitoring:**\n"
                    for device in test_specific_devices[:3]:  # Limit to top 3
                        summary += f"- {device['name']} - {device['description']}\n"
                        summary += f"  Price range: {device['price_range']}\n"
                        summary += f"  Recommended brands: {', '.join(device['recommended_brands'][:3])}\n"
                    summary += "\n"

                # Add device recommendations
                single_devices = result.get("single_device_recommendations", [])
                multi_devices = result.get("multi_device_recommendations", [])

                if multi_devices:
                    summary += "**Recommended multi-function devices:**\n"
                    for device in multi_devices[:2]:  # Limit to top 2
                        summary += f"- {device['name']} - {device['description']}\n"
                        summary += f"  Price range: {device['price_range']}\n"
                        summary += f"  Recommended brands: {', '.join(device['recommended_brands'][:3])}\n"
                    summary += "\n"

                if single_devices:
                    summary += "**Recommended single-function devices:**\n"
                    for device in single_devices[:3]:  # Limit to top 3
                        summary += f"- {device['name']} - {device['description']}\n"
                        summary += f"  Price range: {device['price_range']}\n"
                        summary += f"  Recommended brands: {', '.join(device['recommended_brands'][:3])}\n"

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error generating device recommendations: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

@app.post("/device-confirmation")
async def device_confirmation_endpoint(request: DeviceConfirmationRequest):
    """Handle user confirmation for device recommendations and provide redirect to turbomedics.com/products"""
    try:
        user_id = request.user_id
        confirmed = request.confirmed

        # Create input data for the confirmation tool
        input_data = {"confirmed": confirmed}

        # Use the device confirmation tool
        result = json.loads(confirm_device_recommendation(json.dumps(input_data)))

        # Update chat history if user exists
        if user_id in chat_histories:
            if confirmed:
                # Add user confirmation message
                chat_histories[user_id].append({
                    "role": "user",
                    "content": "Yes, I'd like to explore these health devices."
                })

                # Add assistant response with redirect information
                response_message = result.get("message", "")
                redirect_message = result.get("redirect_message", "")

                if redirect_message:
                    response_message += f"\n\n{redirect_message}"

                # Add store features if available
                if "additional_info" in result and "store_features" in result["additional_info"]:
                    response_message += "\n\n**Store Features:**\n"
                    for feature in result["additional_info"]["store_features"]:
                        response_message += f"• {feature}\n"

                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": response_message
                })
            else:
                # Add user decline message
                chat_histories[user_id].append({
                    "role": "user",
                    "content": "No, I don't need device recommendations right now."
                })

                # Add assistant response for decline
                response_message = result.get("message", "")
                if "alternative_suggestions" in result:
                    response_message += "\n\n**Alternative options:**\n"
                    for suggestion in result["alternative_suggestions"]:
                        response_message += f"• {suggestion}\n"

                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": response_message
                })

        return result
    except Exception as e:
        error_msg = f"Error processing device confirmation: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

@app.post("/symptom-checker")
async def symptom_checker_endpoint(request: SymptomCheckerRequest):
    """Analyze symptoms and provide potential diagnoses, recommended tests, and home care advice"""
    try:
        user_id = request.user_id
        symptoms = request.symptoms

        if not symptoms:
            return {"error": "No symptoms provided."}

        # Create input data for the symptom checker
        input_data = {
            "symptoms": symptoms,
            "age": request.age,
            "sex": request.sex,
            "duration": request.duration,
            "severity": request.severity
        }

        # Use the symptom checker tool
        result = json.loads(analyze_symptoms(json.dumps(input_data)))

        # Save symptom checker data to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["symptom_checker"] = {
            "data": input_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved symptom checker data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": f"I'm experiencing these symptoms: {symptoms}"
            })

            # Create a summary of the analysis for the assistant's response
            summary = "Based on the symptoms you've described, here's my assessment:\n\n"

            # Check for emergency level
            if result.get("urgency_level") == "emergency":
                summary += "🚨 **URGENT MEDICAL ATTENTION NEEDED** 🚨\n\n"
                summary += f"{result.get('message', 'Please seek immediate medical care.')}\n\n"
                summary += f"{result.get('recommendation', '')}\n\n"

                # Add assistant message
                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": summary
                })

                return result

            # Add potential conditions
            potential_conditions = result.get("potential_conditions", [])
            if potential_conditions:
                summary += "**Potential conditions to discuss with your healthcare provider:**\n"
                for condition in potential_conditions:
                    summary += f"- {condition}\n"
                summary += "\n"

            # Add urgency level with appropriate emoji
            urgency_level = result.get("urgency_level", "low")
            if urgency_level == "high":
                summary += "**Urgency Level: 🚨 High** - Please consult with a healthcare provider soon.\n\n"
            elif urgency_level == "moderate":
                summary += "**Urgency Level: ⚠️ Moderate** - Consider scheduling an appointment with your healthcare provider.\n\n"
            else:
                summary += "**Urgency Level: ✅ Low** - Monitor your symptoms and consult a healthcare provider if they worsen.\n\n"

            # Add recommended tests
            recommended_tests = result.get("recommended_tests", [])
            if recommended_tests:
                summary += "**Tests your healthcare provider might consider:**\n"
                for test in recommended_tests:
                    summary += f"- {test}\n"
                summary += "\n"

            # Add home care recommendations
            home_care = result.get("home_care_recommendations", [])
            if home_care:
                summary += "**Self-care recommendations:**\n"
                for care in home_care:
                    summary += f"- {care}\n"
                summary += "\n"

            # Add disclaimer
            summary += "**Important:** This is not a medical diagnosis. The information provided is for educational purposes only and should not replace professional medical advice. Please consult with a healthcare provider for proper evaluation and treatment."

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error analyzing symptoms: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

@app.post("/lab-test-explainer")
async def lab_test_explainer_endpoint(request: LabTestExplainerRequest):
    """Explain lab tests, including what they measure, normal ranges, and patient education"""
    try:
        user_id = request.user_id

        # If list_tests is True, return a list of available tests
        if request.list_tests:
            logging.info("Listing available lab tests")
            result_json = list_available_tests()
            result = json.loads(result_json)
            return result

        # If test_name is provided, explain the specific test
        elif request.test_name:
            logging.info(f"Explaining lab test: {request.test_name}")
            result_json = explain_lab_test(request.test_name)
            result = json.loads(result_json)

            # Update chat history if user exists
            if user_id in chat_histories:
                # Add user message
                chat_histories[user_id].append({
                    "role": "user",
                    "content": f"Can you explain what the {request.test_name} test measures and what the normal ranges are?"
                })

                # Create a summary of the lab test explanation for the assistant's response
                if result.get("found", False):
                    summary = f"**{result['test_name']} Explained**\n\n"

                    # Add note if this was a fuzzy match
                    if "note" in result:
                        summary += f"*{result['note']}*\n\n"

                    # Add description
                    summary += f"{result['description']}\n\n"

                    # Add what it measures
                    if "what_it_measures" in result:
                        summary += "**What it measures:**\n"
                        for item in result["what_it_measures"]:
                            summary += f"- {item}\n"
                        summary += "\n"

                    # Add normal ranges
                    if "normal_ranges" in result:
                        summary += "**Normal ranges:**\n"
                        for param, range_val in result["normal_ranges"].items():
                            summary += f"- {param}: {range_val}\n"
                        summary += "\n"

                    # Add when it's ordered
                    if "when_ordered" in result:
                        summary += "**When this test is ordered:**\n"
                        for item in result["when_ordered"][:3]:  # Limit to 3 items
                            summary += f"- {item}\n"
                        summary += "\n"

                    # Add patient education
                    if "patient_education" in result:
                        summary += f"**Patient education:**\n{result['patient_education']}\n\n"

                    # Add preparation
                    if "preparation" in result:
                        summary += f"**Preparation:**\n{result['preparation']}\n\n"

                    # Add interpretation
                    if "interpretation" in result:
                        summary += f"**Interpretation:**\n{result['interpretation']}"
                else:
                    summary = f"I'm sorry, but I don't have information about the '{request.test_name}' test in my database. Please try another test name or ask for a list of available tests."

                # Add assistant message
                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": summary
                })

            return result

        # If neither list_tests nor test_name is provided, return an error
        else:
            error_msg = "Either test_name or list_tests must be provided"
            logging.error(error_msg)
            return {"error": error_msg}
    except Exception as e:
        error_msg = f"Error processing lab test explainer request: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

# Removed /chronic_tracker endpoint - using /track-chronic-condition instead

def process_chronic_tracker(user_id, condition_data):
    """Process chronic condition tracking data"""
    try:
        logging.info(f"Processing chronic tracker data for user {user_id}: {condition_data}")

        # Get historical data for this user and condition type
        historical_data = []
        condition_type = condition_data.get("condition_type", "")
        logging.info(f"Condition type: {condition_type}")

        if user_id in user_health_data and "chronic_tracker" in user_health_data[user_id]:
            # Filter historical data by condition type
            for entry in user_health_data[user_id]["chronic_tracker"]:
                if entry["data"].get("condition_type") == condition_type:
                    historical_data.append({
                        "condition_data": entry["data"],
                        "timestamp": entry["timestamp"]
                    })

        # Prepare input data for the tool
        input_data = {
            "user_id": user_id,
            "condition_data": condition_data,
            "historical_data": historical_data
        }

        # Use the chronic tracker tool
        logging.info(f"Calling chronic_tracker_tool with input: {json.dumps(input_data)}")
        try:
            tool_result = chronic_tracker_tool(json.dumps(input_data))
            logging.info(f"Received raw result from chronic_tracker_tool: {tool_result}")
            result = json.loads(tool_result)
            logging.info(f"Parsed result: {json.dumps(result)}")
        except Exception as e:
            logging.error(f"Error calling chronic_tracker_tool: {str(e)}")
            logging.error(traceback.format_exc())
            return {"error": f"Error calling chronic_tracker_tool: {str(e)}"}

        return result
    except Exception as e:
        error_msg = f"Error processing chronic tracker data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

# Add a new endpoint for chronic condition tracking
@app.post("/track-chronic-condition")
async def track_chronic_condition(request: ChronicTrackerRequest):
    """Track chronic conditions over time, analyze trends, and provide personalized feedback with support for daily/weekly tracking"""
    try:
        logging.info(f"Received chronic condition tracking request: {request}")
        user_id = request.user_id
        condition_data = request.condition_data
        tracking_frequency = request.tracking_frequency
        measurement_date = request.measurement_date

        if not condition_data:
            return {"error": "No condition data provided."}

        # Ensure condition_type is provided
        if "condition_type" not in condition_data:
            return {"error": "condition_type is required in condition_data."}

        # Add tracking frequency and measurement date to condition data
        condition_data["tracking_frequency"] = tracking_frequency

        # Use provided measurement date or current time
        if measurement_date:
            condition_data["measurement_date"] = measurement_date
        else:
            condition_data["measurement_date"] = datetime.now().isoformat()

        # Process the chronic condition data
        result = process_chronic_tracker(user_id, condition_data)

        # Save chronic tracker data to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}

        # Initialize chronic_tracker as a list if it doesn't exist
        if "chronic_tracker" not in user_health_data[user_id]:
            user_health_data[user_id]["chronic_tracker"] = []

        # Add the new entry to the list
        user_health_data[user_id]["chronic_tracker"].append({
            "data": condition_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        })

        logging.info(f"Saved chronic tracker data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            condition_type = condition_data.get("condition_type", "")
            frequency_text = ""

            # Add frequency information to the user message
            if tracking_frequency == "daily":
                frequency_text = "daily "
            elif tracking_frequency == "weekly":
                frequency_text = "weekly "
            elif tracking_frequency == "monthly":
                frequency_text = "monthly "

            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": f"I've submitted my {frequency_text}{condition_type} tracking data for analysis."
            })

            # Create a summary of the analysis for the assistant's response
            summary = f"I've analyzed your {condition_type} tracking data.\n\n"

            # Add summary from the result
            if "summary" in result:
                summary += result["summary"]
            else:
                # Create a basic summary if the detailed one isn't available
                if "current_analysis" in result:
                    current = result["current_analysis"]

                    # Add glucose status if available
                    if "glucose" in current:
                        glucose_status = current["glucose"]["status"]
                        glucose_desc = current["glucose"]["description"]
                        summary += f"**Glucose:** {glucose_status.title()} - {glucose_desc}\n\n"

                    # Add HbA1c status if available
                    if "hba1c" in current:
                        hba1c_status = current["hba1c"]["status"]
                        hba1c_desc = current["hba1c"]["description"]
                        summary += f"**HbA1c:** {hba1c_status.title()} - {hba1c_desc}\n\n"

                # Add trend analysis if available
                if "trend_analysis" in result:
                    trends = result["trend_analysis"]

                    # Add tracking frequency and consistency information
                    if "tracking_frequency" in trends:
                        freq = trends["tracking_frequency"]
                        if freq != "as_needed":
                            summary += f"**Tracking Frequency:** {freq.title()}\n"

                            if "consistency_score" in trends:
                                score = trends["consistency_score"]
                                if score >= 90:
                                    summary += f"**Consistency:** Excellent ({score}/100)\n\n"
                                elif score >= 75:
                                    summary += f"**Consistency:** Good ({score}/100)\n\n"
                                elif score >= 50:
                                    summary += f"**Consistency:** Fair ({score}/100)\n\n"
                                else:
                                    summary += f"**Consistency:** Needs improvement ({score}/100)\n\n"

                    # Add glucose trend information
                    if "glucose" in trends and trends["glucose"]:
                        glucose_trend = trends["glucose"]["direction"]
                        glucose_desc = trends["glucose"]["description"]
                        summary += f"**Glucose Trend:** {glucose_trend.title()} - {glucose_desc}\n\n"

                        # Add frequency-specific insights if available
                        if "frequency_analysis" in trends["glucose"] and trends["glucose"]["frequency_analysis"]:
                            analysis = trends["glucose"]["frequency_analysis"]

                            # Add daily pattern insights
                            if "insights" in analysis and analysis["insights"]:
                                summary += "**Pattern Analysis:**\n"
                                for i, insight in enumerate(analysis["insights"][:2], 1):  # Limit to first 2 insights
                                    summary += f"- {insight}\n"
                                summary += "\n"

                    # Add HbA1c trend information
                    if "hba1c" in trends and trends["hba1c"]:
                        hba1c_trend = trends["hba1c"]["direction"]
                        hba1c_desc = trends["hba1c"]["description"]
                        summary += f"**HbA1c Trend:** {hba1c_trend.title()} - {hba1c_desc}\n\n"

                # Add recommendations
                if "recommendations" in result:
                    recommendations = result["recommendations"]
                    if recommendations:
                        # Filter out section headers (they start with newline)
                        main_recommendations = [rec for rec in recommendations if not rec.startswith("\n")]

                        # Find section indices
                        tracking_section_index = -1
                        continuous_care_section_index = -1

                        for i, rec in enumerate(recommendations):
                            if rec == "\nFor better tracking:":
                                tracking_section_index = i
                            elif rec == "\nFor continuous care:":
                                continuous_care_section_index = i

                        # Add main recommendations
                        summary += "**Recommendations:**\n"
                        end_index = min(5, tracking_section_index if tracking_section_index > 0 else len(main_recommendations))
                        for i, rec in enumerate(main_recommendations[:end_index], 1):
                            summary += f"{i}. {rec}\n"

                        # Add tracking recommendations section if it exists
                        if tracking_section_index > 0:
                            summary += "\n**For Better Tracking:**\n"
                            start_idx = tracking_section_index + 1
                            end_idx = continuous_care_section_index if continuous_care_section_index > 0 else len(recommendations)
                            for rec in recommendations[start_idx:end_idx]:
                                if not rec.startswith("\n"):  # Skip any nested section headers
                                    summary += f"• {rec}\n"

                        # Add continuous care recommendations section if it exists
                        if continuous_care_section_index > 0:
                            summary += "\n**For Continuous Care:**\n"
                            for rec in recommendations[continuous_care_section_index+1:]:
                                if not rec.startswith("\n"):  # Skip any nested section headers
                                    summary += f"• {rec}\n"

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error tracking chronic condition: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

# Add a simple test endpoint
@app.get("/test-endpoint")
async def test_endpoint():
    """Test endpoint to verify server functionality"""
    return {"message": "Test endpoint is working"}

# Add follow-up reminder endpoint
@app.post("/followup-reminder")
async def followup_reminder_endpoint(request: FollowupReminderRequest):
    """Generate follow-up reminders based on past health data and abnormal values, with optional test-specific filtering"""
    try:
        user_id = request.user_id
        health_data = request.health_data
        test_type = request.test_type

        # If health data is not provided, use stored health data
        if not health_data and user_id in user_health_data:
            # If test_type is provided, only use that specific test data
            if test_type and test_type in user_health_data[user_id]:
                health_data = user_health_data[user_id][test_type].get("data", {})
                logging.info(f"Using test-specific data for {test_type}")
            else:
                # Combine all health data for the user
                health_data = {}

                # Add vital signs
                if "vital_signs" in user_health_data[user_id]:
                    health_data.update(user_health_data[user_id]["vital_signs"].get("data", {}))

                # Add health score data
                if "health_score" in user_health_data[user_id]:
                    health_data.update(user_health_data[user_id]["health_score"].get("data", {}))

                # Add kidney function data
                if "kidney_function" in user_health_data[user_id]:
                    health_data.update(user_health_data[user_id]["kidney_function"].get("data", {}))

                # Add lipid profile data
                if "lipid_profile" in user_health_data[user_id]:
                    health_data.update(user_health_data[user_id]["lipid_profile"].get("data", {}))

                # Add lung capacity data
                if "lung_capacity" in user_health_data[user_id]:
                    health_data.update(user_health_data[user_id]["lung_capacity"].get("data", {}))

                # Add test results data
                if "test_results" in user_health_data[user_id]:
                    health_data.update(user_health_data[user_id]["test_results"].get("data", {}))

        # If no health data is available, return an error
        if not health_data:
            return {
                "error": "No health data available for this user. Please enter some health data first.",
                "summary": "I don't have enough health data to generate follow-up reminders. Please use the quick action buttons to enter your health data first."
            }

        # Prepare input data for the tool
        input_data = {
            "user_id": user_id,
            "health_data": health_data
        }

        # Add test_type if provided
        if test_type:
            input_data["test_type"] = test_type

        # Use the followup reminder tool
        logging.info(f"Calling followup_reminder_tool with input: {json.dumps(input_data)}")
        try:
            tool_result = followup_reminder_tool(json.dumps(input_data))
            logging.info(f"Received raw result from followup_reminder_tool: {tool_result}")
            result = json.loads(tool_result)
            logging.info(f"Parsed result: {json.dumps(result)}")
        except Exception as e:
            logging.error(f"Error calling followup_reminder_tool: {str(e)}")
            logging.error(traceback.format_exc())
            return {"error": f"Error calling followup_reminder_tool: {str(e)}"}

        # Save follow-up reminder data to user health data store
        if user_id not in user_health_data:
            user_health_data[user_id] = {}

        user_health_data[user_id]["followup_reminder"] = {
            "reminders": result.get("reminders", []),
            "follow_up_schedule": result.get("follow_up_schedule", {}),
            "care_continuity": result.get("care_continuity", []),
            "timestamp": datetime.now().isoformat()
        }

        logging.info(f"Saved follow-up reminder data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Create a test-specific message if test_type is provided
            if test_type:
                test_type_name = {
                    "vital_signs": "vital signs",
                    "kidney_function": "kidney function",
                    "lipid_profile": "lipid profile",
                    "lung_capacity": "respiratory health",
                    "test_results": "test results",
                    "realtime_health_score": "health score"
                }.get(test_type, "health")

                user_message = f"I'd like to see follow-up reminders for my {test_type_name} results."
            else:
                user_message = "I'd like to see my follow-up reminders and care continuity plan."

            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": user_message
            })

            # Create a test-specific response if test_type is provided
            if test_type:
                test_type_name = {
                    "vital_signs": "Vital Signs",
                    "kidney_function": "Kidney Function",
                    "lipid_profile": "Cardiovascular Health",
                    "lung_capacity": "Respiratory Health",
                    "test_results": "Test Results",
                    "realtime_health_score": "Health Score"
                }.get(test_type, "Health")

                # Add test_type to the result for the summary
                result["test_type"] = test_type

                # Add assistant message with test-specific title
                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": f"**{test_type_name} Follow-up Reminders**\n\n{result.get('summary', 'Here are your follow-up reminders based on your test results.')}"
                })
            else:
                # Add generic assistant message
                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": result.get("summary", "Here are your follow-up reminders and care continuity plan.")
                })

        return result
    except Exception as e:
        error_msg = f"Error generating follow-up reminders: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

@app.post("/chronic-risk")
async def chronic_risk_endpoint(request: ChronicRiskRequest):
    try:
        user_id = request.user_id
        data = request.chronic_data

        # Run risk prediction
        result = predict_chronic_risk(data)

        # Save to health data
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["chronic_risk"] = {
            "input": data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Log chat interaction if enabled
        if user_id in chat_histories:
            chat_histories[user_id].append({
                "role": "user",
                "content": f"My chronic risk inputs are: {json.dumps(data)}"
            })
            chat_histories[user_id].append({
                "role": "assistant",
                "content": f"Here is your chronic disease risk prediction:\n{json.dumps(result, indent=2)}"
            })

        return result
    except Exception as e:
        logging.error(f"Chronic risk analysis failed for {request.user_id}: {str(e)}")
        return {"error": str(e)}

@app.post("/summarize-medical-doc")
async def summarize_medical_doc(
    user_id: str = Form(...),
    model: Optional[str] = Form(DEFAULT_MODEL),  # Optional model selection
    file: UploadFile = File(...)
):
    try:
        # Use the updated extract_text_from_upload function that supports both PDF and DOCX
        raw_text = extract_text_from_upload(file)

        if len(raw_text) < 100:
            return {"error": "Unable to extract enough readable text. Please try another document."}

        # Use selected or default model
        summary = summarize_medical_text(raw_text, model=model)

        # Determine file type
        file_type = "PDF" if file.filename.lower().endswith(".pdf") else "DOCX" if file.filename.lower().endswith(".docx") else "document"

        # Store in chat history
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": f"I've uploaded a medical {file_type} document: {file.filename}"})
            chat_histories[user_id].append({"role": "assistant", "content": summary})

        return {
            "summary": summary,
            "length": len(raw_text),
            "file_type": file_type,
            "filename": file.filename,
            "success": True
        }

    except ValueError as ve:
        # Handle unsupported file type error
        logging.error(f"Unsupported file type: {ve}")
        return {"error": str(ve)}
    except Exception as e:
        logging.error(f"Error summarizing doc: {e}")
        return {"error": str(e)}

@app.post("/track-lifestyle")
async def track_lifestyle_habits(request: LifestyleHabitRequest):
    try:
        user_id = request.user_id
        habits = request.habits

        record_habits(user_id, habits)
        summary = compute_weekly_habit_summary(user_id)
        recommendations = generate_lifestyle_recommendations(summary)

        # Update chat history
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": f"My lifestyle habits are: {habits}"})
            chat_histories[user_id].append({"role": "assistant", "content": "I've logged your habits and generated a weekly summary."})

        return {
            "weekly_summary": summary,
            "recommendations": recommendations
        }

    except Exception as e:
        logging.error(f"Error processing lifestyle habits: {str(e)}")
        return {"error": str(e)}


@app.post("/weekly-digest")
async def summarize_weekly_vitals(request: DigestRequest):
    try:
        user_id = request.user_id
        data = request.vital_signs
        timestamp = datetime.now().isoformat()

        # Add the new vitals to the user's history
        if user_id not in user_health_data:
            user_health_data[user_id] = []

        user_health_data[user_id].append({**data, "timestamp": timestamp})

        # Call digest generator tool
        digest = generate_weekly_digest(user_id, user_health_data)

        return {
            "current_vitals": data,
            "weekly_digest": digest,
            "records_logged": len(user_health_data[user_id])
        }

    except Exception as e:
        return {"error": str(e)}

@app.post("/mental-health-assessment")
async def mental_health_assessment_endpoint(request: MentalHealthAssessmentRequest):
    """
    Comprehensive Mental Health Assessment endpoint
    Includes stress/burnout, PHQ-9, GAD-7, and ML risk prediction
    """
    try:
        user_id = request.user_id
        assessment_data = request.assessment_data

        logging.info(f"Processing mental health assessment for user {user_id}")
        logging.info(f"Assessment data: {json.dumps(assessment_data)}")

        # Initialize the mental health assessment tool
        mental_health_tool = MentalHealthAssessmentTool()

        # Validate that country is provided
        if "country" not in assessment_data or not assessment_data["country"]:
            return {"error": "Country is required for mental health assessment and crisis resource recommendations"}

        # Perform comprehensive assessment
        result = mental_health_tool.comprehensive_assessment(assessment_data)

        # Save assessment results to user health data
        if user_id not in user_health_data:
            user_health_data[user_id] = {}

        user_health_data[user_id]["mental_health_assessment"] = {
            "input": assessment_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Update chat history if user exists
        if user_id in chat_histories:
            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": "I'd like to complete a comprehensive mental health assessment."
            })

            # Create a summary for the chat
            summary = result.get("summary", "Mental health assessment completed.")
            risk_level = result.get("assessments", {}).get("ml_risk_prediction", {}).get("risk_level", "Unknown")

            # Add assistant message with assessment summary
            chat_histories[user_id].append({
                "role": "assistant",
                "content": f"**Mental Health Assessment Complete**\n\n{summary}\n\n**Risk Level**: {risk_level}\n\nI've provided personalized recommendations and follow-up reminders based on your assessment. If you're experiencing a mental health crisis, please reach out to the crisis resources provided."
            })

        logging.info(f"Mental health assessment completed for user {user_id}")
        return result

    except Exception as e:
        error_msg = f"Error in mental health assessment: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}


@app.get("/mental-health-countries")
async def get_mental_health_countries():
    """
    Get list of supported countries for mental health crisis resources
    """
    try:
        from tools.tools_mental_health_assessment import MentalHealthAssessmentTool
        mental_health_tool = MentalHealthAssessmentTool()
        countries = mental_health_tool.get_supported_countries()

        return {
            "supported_countries": countries,
            "total_countries": len(countries),
            "note": "These countries have specific crisis resources available. Other countries will receive generic international resources."
        }
    except Exception as e:
        logging.error(f"Error getting supported countries: {str(e)}")
        return {"error": f"Failed to get supported countries: {str(e)}"}

@app.post("/liver-function/manual")
async def analyze_liver_manual(request: LiverFunctionAssessmentRequest):
    try:
        user_id = request.user_id
        values = {
        "ALT (SGPT)": request.lft_data.ALT_SGPT,
        "AST (SGOT)": request.lft_data.AST_SGOT,
        "ALP": request.lft_data.ALP,
        "GGT": request.lft_data.GGT,
        "Total Bilirubin": request.lft_data.Total_Bilirubin,
        "Direct Bilirubin": request.lft_data.Direct_Bilirubin,
        "Albumin": request.lft_data.Albumin,
        "INR": request.lft_data.INR,
        "Ammonia": request.lft_data.Ammonia,
        "LDH": request.lft_data.LDH,
        "Globulin": request.lft_data.Globulin,
        "A/G Ratio": request.lft_data.AG_Ratio,
        "ALT:AST Ratio": request.lft_data.ALT_AST_Ratio,
        "Indirect Bilirubin": request.lft_data.Indirect_Bilirubin,
        "Total Protein": request.lft_data.Total_Protein,
    }

        result = analyze_liver_function(
            extracted_values=values,
            dietary_habits=request.lft_data.dietary_habits,
            medications=request.lft_data.medications,
            symptoms=request.lft_data.symptoms,
            hepatitis_markers=request.lft_data.hepatitis_markers,
            smoking_alcohol_use=request.lft_data.smoking_alcohol_use,
            medical_conditions=request.lft_data.medical_conditions,
            input_method="Manual Entry"
        )

        # Store in user memory
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["liver_function"] = {
            "data": values,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Add to chat summary
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": "Please assess my liver function."})
            summary = "\n".join(result["parameter_status"])
            summary += f"\n\n**Risk Level**: {result['risk_level']}\n**Confidence**: {result['confidence_level']}\n\n**Recommendations:**\n- " + "\n- ".join(result["recommendations"])

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_id].append({"role": "assistant", "content": summary})

        return result

    except Exception as e:
        logging.error(f"Liver Function Error: {str(e)}")
        return {"error": str(e)}


@app.post("/liver-function/pdf")
async def analyze_liver_from_pdf(user_id: str, file: UploadFile = File(...)):
    try:
        import pdfplumber
        with pdfplumber.open(file.file) as pdf:
            text = "\n".join(page.extract_text() for page in pdf.pages if page.extract_text())
        values = extract_lft_values(text)

        result = analyze_liver_function(
            extracted_values=values,
            input_method="Upload PDF"
        )

        # Store in memory
        if user_id not in user_health_data:
            user_health_data[user_id] = {}
        user_health_data[user_id]["liver_function"] = {
            "data": values,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Chat memory
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": "Please analyze my liver function test PDF."})
            summary = "\n".join(result["parameter_status"])
            summary += f"\n\n**Risk Level**: {result['risk_level']}\n**Confidence**: {result['confidence_level']}\n\n**Recommendations:**\n- " + "\n- ".join(result["recommendations"])

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_id].append({"role": "assistant", "content": summary})

        return {"extracted_values": values, **result}

    except Exception as e:
        logging.error(f"Liver Function PDF Error: {str(e)}")
        return {"error": str(e)}

# === RUN SERVER ===
if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8002)
