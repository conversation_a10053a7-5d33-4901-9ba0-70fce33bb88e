import streamlit as st
import requests
import json
import uuid
import numpy as np
import re
import os
import sys
from datetime import datetime

# === CONSTANTS ===
API_URL = "http://127.0.0.1:8001"
MODELS = ["qwen2.5:1.5b", "deepseek-r1:1.5b"]
DEFAULT_MODEL = "qwen2.5:1.5b"

# === STYLING ===
st.markdown("""
<style>
    .main {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    .sidebar .sidebar-content {
        background-color: #2d2d2d;
    }
    .stTextInput textarea {
        color: #ffffff !important;
    }
    .stSelectbox div[data-baseweb="select"] {
        color: white !important;
        background-color: #3d3d3d !important;
    }
    .stSelectbox svg {
        fill: white !important;
    }
    .stSelectbox option {
        background-color: #2d2d2d !important;
        color: white !important;
    }
    div[role="listbox"] div {
        background-color: #2d2d2d !important;
        color: white !important;
    }
    .chat-title {
        margin-bottom: 0;
    }

    /* Enhanced button styling */
    .stButton button {
        background-color: #2c3e50;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 10px 15px;
        font-weight: 500;
        transition: all 0.3s ease;
        width: 100%;
        margin-bottom: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    .stButton button:hover {
        background-color: #3498db;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    /* Button category styling */
    .assessment-btn button {
        background-color: #2980b9;
    }
    .assessment-btn button:hover {
        background-color: #3498db;
    }

    .monitoring-btn button {
        background-color: #27ae60;
    }
    .monitoring-btn button:hover {
        background-color: #2ecc71;
    }

    .analysis-btn button {
        background-color: #8e44ad;
    }
    .analysis-btn button:hover {
        background-color: #9b59b6;
    }

    .support-btn button {
        background-color: #d35400;
    }
    .support-btn button:hover {
        background-color: #e67e22;
    }

    /* Category headers */
    .category-header {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #ecf0f1;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .health-metrics {
        background-color: #2d2d2d;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    .quick-actions {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
    }
</style>
""", unsafe_allow_html=True)

# === SESSION STATE INITIALIZATION ===
if "user_id" not in st.session_state:
    st.session_state.user_id = str(uuid.uuid4())

if "session_id" not in st.session_state:
    st.session_state.session_id = str(uuid.uuid4())

if "message_log" not in st.session_state:
    st.session_state.message_log = [{"role": "ai", "content": "Hi! I'm Dr. Deuce. How can I help you today?"}]

if "chat_title" not in st.session_state:
    st.session_state.chat_title = "New Chat"

if "server_status" not in st.session_state:
    st.session_state.server_status = "Checking..."

if "waiting_for_vitals" not in st.session_state:
    st.session_state.waiting_for_vitals = False

if "waiting_for_kidney_function" not in st.session_state:
    st.session_state.waiting_for_kidney_function = False

if "waiting_for_lipid_profile" not in st.session_state:
    st.session_state.waiting_for_lipid_profile = False

if "waiting_for_lung_capacity" not in st.session_state:
    st.session_state.waiting_for_lung_capacity = False

if "waiting_for_liver_function" not in st.session_state:
    st.session_state.waiting_for_liver_function = False

if "waiting_for_test_results" not in st.session_state:
    st.session_state.waiting_for_test_results = False

if "current_test_type" not in st.session_state:
    st.session_state.current_test_type = "Malaria Test"

if "test_selection_step" not in st.session_state:
    st.session_state.test_selection_step = False

if "test_form_step" not in st.session_state:
    st.session_state.test_form_step = False

if "malaria_form_step" not in st.session_state:
    st.session_state.malaria_form_step = False

if "widal_form_step" not in st.session_state:
    st.session_state.widal_form_step = False

if "waiting_for_confirmation" not in st.session_state:
    st.session_state.waiting_for_confirmation = False

if "confirmation_type" not in st.session_state:
    st.session_state.confirmation_type = None

if "waiting_for_health_consultation" not in st.session_state:
    st.session_state.waiting_for_health_consultation = False

if "waiting_for_realtime_health_score" not in st.session_state:
    st.session_state.waiting_for_realtime_health_score = False

if "waiting_for_device_recommendation" not in st.session_state:
    st.session_state.waiting_for_device_recommendation = False

if "waiting_for_symptom_checker" not in st.session_state:
    st.session_state.waiting_for_symptom_checker = False

if "symptom_checker_step" not in st.session_state:
    st.session_state.symptom_checker_step = 1

if "waiting_for_lab_test_explainer" not in st.session_state:
    st.session_state.waiting_for_lab_test_explainer = False

if "lab_test_explainer_step" not in st.session_state:
    st.session_state.lab_test_explainer_step = 1

if "waiting_for_chronic_tracker" not in st.session_state:
    st.session_state.waiting_for_chronic_tracker = False

if "chronic_condition_type" not in st.session_state:
    st.session_state.chronic_condition_type = "diabetes"

if "chronic_tracker_data" not in st.session_state:
    st.session_state.chronic_tracker_data = {}

if "chronic_form_submitted" not in st.session_state:
    st.session_state.chronic_form_submitted = False

if "chronic_tracker_results" not in st.session_state:
    st.session_state.chronic_tracker_results = None

if "waiting_for_followup_reminder" not in st.session_state:
    st.session_state.waiting_for_followup_reminder = False

if "waiting_for_followup_confirmation" not in st.session_state:
    st.session_state.waiting_for_followup_confirmation = False

if "current_recommendation_context" not in st.session_state:
    st.session_state.current_recommendation_context = None

if "followup_context" not in st.session_state:
    st.session_state.followup_context = None

if "waiting_for_doc_summarizer" not in st.session_state:
    st.session_state.waiting_for_doc_summarizer = False

if "waiting_for_chronic_risk" not in st.session_state:
    st.session_state.waiting_for_chronic_risk = False



if "waiting_for_lifestyle_tracking" not in st.session_state:
    st.session_state.waiting_for_lifestyle_tracking = False

if "chronic_risk_data" not in st.session_state:
    st.session_state.chronic_risk_data = {}

if "lifestyle_habits_data" not in st.session_state:
    st.session_state.lifestyle_habits_data = {}

# New session state variables for the new tools
if "chronic_risk_form_submitted" not in st.session_state:
    st.session_state.chronic_risk_form_submitted = False

if "chronic_risk_results" not in st.session_state:
    st.session_state.chronic_risk_results = None

if "doc_summary_results" not in st.session_state:
    st.session_state.doc_summary_results = None



if "lifestyle_tracking_results" not in st.session_state:
    st.session_state.lifestyle_tracking_results = None

# Progress Tracker session state variables
if "waiting_for_progress_tracker" not in st.session_state:
    st.session_state.waiting_for_progress_tracker = False

if "progress_tracker_results" not in st.session_state:
    st.session_state.progress_tracker_results = None

# Weekly Digest session state variables
if "waiting_for_weekly_digest" not in st.session_state:
    st.session_state.waiting_for_weekly_digest = False

if "weekly_digest_results" not in st.session_state:
    st.session_state.weekly_digest_results = None

# Mental Health Assessment session state variables
if "waiting_for_mental_health_assessment" not in st.session_state:
    st.session_state.waiting_for_mental_health_assessment = False

if "mental_health_assessment_step" not in st.session_state:
    st.session_state.mental_health_assessment_step = 1

if "mental_health_assessment_data" not in st.session_state:
    st.session_state.mental_health_assessment_data = {}

if "mental_health_assessment_results" not in st.session_state:
    st.session_state.mental_health_assessment_results = None

# Health data is stored on the server side

# === HELPER FUNCTIONS ===
# Recommendations are now handled by the server

def request_chronic_risk_prediction(chronic_data):
    """Submit chronic risk data for prediction"""
    try:
        # Log the data being sent
        print(f"Submitting chronic risk data: {json.dumps(chronic_data, indent=2)}")

        payload = {
            "user_id": st.session_state.user_id,
            "chronic_data": chronic_data
        }

        # Make the request
        response = requests.post(f"{API_URL}/chronic-risk", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received chronic risk prediction: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}



def summarize_medical_document(file_data, user_id=None, model=None):
    """Submit a medical document for summarization"""
    if user_id is None:
        user_id = st.session_state.user_id
    if model is None:
        model = DEFAULT_MODEL

    try:
        # Create form data
        form_data = {
            "user_id": user_id,
            "model": model
        }

        files = {
            "file": file_data
        }

        # Make the request
        response = requests.post(f"{API_URL}/summarize-medical-doc", data=form_data, files=files)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received document summary: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def track_lifestyle_habits(habits_data, user_id=None):
    """Submit lifestyle habits for tracking and analysis"""
    if user_id is None:
        user_id = st.session_state.user_id

    try:
        # Flatten the nested structure into a flat dictionary of string keys to float values
        flattened_habits = {}

        # Extract values from the nested structure
        if "physical_activity" in habits_data:
            flattened_habits["exercise_duration"] = float(habits_data["physical_activity"]["exercise_duration"])
            flattened_habits["steps"] = float(habits_data["physical_activity"]["steps"])

        if "nutrition" in habits_data:
            flattened_habits["water_intake"] = float(habits_data["nutrition"]["water_intake"])
            flattened_habits["fruit_veg_servings"] = float(habits_data["nutrition"]["fruit_veg_servings"])
            flattened_habits["processed_food"] = float(habits_data["nutrition"]["processed_food_servings"])

        if "mental_wellbeing" in habits_data:
            flattened_habits["stress_level"] = float(habits_data["mental_wellbeing"]["stress_level"])
            flattened_habits["mindfulness"] = float(habits_data["mental_wellbeing"]["mindfulness_minutes"])
            # Convert mood to a numeric value
            mood_values = {"Excellent": 4.0, "Good": 3.0, "Fair": 2.0, "Poor": 1.0}
            flattened_habits["mood"] = mood_values.get(habits_data["mental_wellbeing"]["mood"], 3.0)

        if "sleep" in habits_data:
            flattened_habits["sleep_duration"] = float(habits_data["sleep"]["duration"])
            # Convert sleep quality to a numeric value
            quality_values = {"Excellent": 4.0, "Good": 3.0, "Fair": 2.0, "Poor": 1.0}
            flattened_habits["sleep_quality"] = quality_values.get(habits_data["sleep"]["quality"], 3.0)
            flattened_habits["consistent_bedtime"] = 1.0 if habits_data["sleep"]["consistent_bedtime"] else 0.0

        # Create payload with flattened habits
        payload = {
            "user_id": user_id,
            "habits": flattened_habits
        }

        # Make the request
        response = requests.post(f"{API_URL}/track-lifestyle", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received lifestyle tracking results: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def track_progress(vital_signs, user_id=None):
    """Submit vital signs for progress tracking and trend analysis"""
    if user_id is None:
        user_id = st.session_state.user_id

    try:
        # Create payload
        payload = {
            "user_id": user_id,
            "vital_signs": vital_signs
        }

        # Make the request
        response = requests.post(f"{API_URL}/track-progress", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received progress tracking results: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def get_weekly_digest(vital_signs, user_id=None):
    """Submit vital signs for weekly digest generation"""
    if user_id is None:
        user_id = st.session_state.user_id

    try:
        # Create payload
        payload = {
            "user_id": user_id,
            "vital_signs": vital_signs
        }

        # Make the request
        response = requests.post(f"{API_URL}/weekly-digest", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received weekly digest results: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def submit_mental_health_assessment(assessment_data, user_id=None, country="United States"):
    """Submit mental health assessment data for comprehensive analysis"""
    if user_id is None:
        user_id = st.session_state.user_id

    try:
        # Create payload
        payload = {
            "user_id": user_id,
            "assessment_data": assessment_data,
            "country": country
        }

        # Make the request
        response = requests.post(f"{API_URL}/mental-health-assessment", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received mental health assessment results: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get(f"{API_URL}/status", timeout=5)
        if response.status_code == 200:
            return "Online ✅", response.json()
        return "Error ❌", None
    except requests.RequestException:
        return "Offline ❌", None

def get_default_health_data():
    """Get default health data from the server"""
    try:
        response = requests.get(f"{API_URL}/default-health-data", timeout=5)
        if response.status_code == 200:
            return response.json()
        print(f"Error getting default health data: {response.status_code}")
        # Return the exact DEFAULT_HEALTH_DATA structure
        return {
            "Glucose": None,
            "SpO2": None,
            "ECG (Heart Rate)": None,
            "Blood Pressure (Systolic)": None,
            "Blood Pressure (Diastolic)": None,
            "Weight (BMI)": None,
            "Temperature": None,
            "Malaria": "Unknown",
            "Widal Test": "Unknown",
            "Hepatitis B": "Unknown",
            "Voluntary Serology": "Unknown",
            "Perfusion_index": None,
            "Waist Circumference": None,
            "Fev": None
        }
    except requests.RequestException as e:
        print(f"Error connecting to server: {str(e)}")
        # Return the exact DEFAULT_HEALTH_DATA structure
        return {
            "Glucose": None,
            "SpO2": None,
            "ECG (Heart Rate)": None,
            "Blood Pressure (Systolic)": None,
            "Blood Pressure (Diastolic)": None,
            "Weight (BMI)": None,
            "Temperature": None,
            "Malaria": "Unknown",
            "Widal Test": "Unknown",
            "Hepatitis B": "Unknown",
            "Voluntary Serology": "Unknown",
            "Perfusion_index": None,
            "Waist Circumference": None,
            "Fev": None
        }

def query_agent(user_query, model):
    """Send a query to the agent, including chat and health history"""
    try:
        # Fetch chat + health history from MCP server
        chat_context = fetch_chat_history()

        payload = {
            "session_id": st.session_state.session_id,
            "user_id": st.session_state.user_id,
            "query": user_query,
            "model": model,
            "chat_history": chat_context  # 🔗 Include prior chat + health data if backend supports it
        }

        response = requests.post(f"{API_URL}/query", json=payload)
        if response.status_code == 200:
            data = response.json()
            if "error" in data:
                return f"Error: {data['error']}"

            # Update chat title if available
            if "chat_title" in data:
                st.session_state.chat_title = data["chat_title"]

            # Check for tool intents
            if "tools_used" in data:
                tools = data.get("tools_used", [])
                if "health_score_intent" in tools or "realtime_health_score_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "realtime_health_score"
                elif "vital_signs_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "vital_signs"
                elif "kidney_function_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "kidney_function"
                elif "lipid_profile_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "lipid_profile"
                elif "lung_capacity_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "lung_capacity"
                elif "liver_function_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "liver_function"

            return data["response"]
        return f"Error: Server returned status code {response.status_code}"
    except requests.RequestException as e:
        return f"Error connecting to server: {str(e)}"

def submit_vital_signs(vital_signs):
    """Submit vital signs to the server"""
    try:
        # Log the data being sent
        print(f"Submitting vital signs: {json.dumps(vital_signs, indent=2)}")

        # Process vital signs to handle "Unknown" values
        processed_vital_signs = {}
        for key, value in vital_signs.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Convert numeric values to float
            else:
                try:
                    if isinstance(value, (int, float)):
                        processed_vital_signs[key] = value
                    else:
                        processed_vital_signs[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_vital_signs[key] = value

        payload = {
            "user_id": st.session_state.user_id,
            "vital_signs": processed_vital_signs
        }

        # Make the request
        response = requests.post(f"{API_URL}/vital-signs", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received response: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def submit_health_score(health_data):
    """Submit health data for score analysis (redirects to realtime-health-score)"""
    try:
        # Log the data being sent
        print(f"Submitting health data: {json.dumps(health_data, indent=2)}")

        # Process health data to handle "Unknown" values
        processed_health_data = {}
        for key, value in health_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle test results with string values
            elif key in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]:
                processed_health_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    if isinstance(value, (int, float)):
                        processed_health_data[key] = value
                    else:
                        processed_health_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_health_data[key] = value

        payload = {
            "user_id": st.session_state.user_id,
            "health_data": processed_health_data,
            "treat_unknown_as_null": "true"
        }

        # Make the request to realtime-health-score instead of health-score
        response = requests.post(f"{API_URL}/realtime-health-score", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received response: {json.dumps(result, indent=2)}")

            # Format the response to match the expected format from the old health-score endpoint
            formatted_result = f"**Total Score: {result.get('Total Score', 0)} Health Status: {result.get('Health Status', 'Unknown')}**\n\n"

            # Format vitals needing improvement
            vitals = result.get('Vitals Needing Improvement', [])
            if isinstance(vitals, list):
                formatted_result += "**Vitals Needing Improvement:** "
                if not vitals or vitals == ["None"]:
                    formatted_result += "None\n\n"
                else:
                    formatted_result += "\n" + "\n".join([f"- {vital}" for vital in vitals]) + "\n\n"
            else:
                formatted_result += f"**Vitals Needing Improvement:** {vitals}\n\n"

            # Format improvement tips
            tips = result.get('Improvement Tips', [])
            if isinstance(tips, list):
                formatted_result += "**Improvement Tips:**"
                if tips:
                    formatted_result += "\n" + "\n".join([f"- {tip}" for tip in tips])
                else:
                    formatted_result += " None"
            else:
                formatted_result += f"**Improvement Tips:** {tips}"

            # Return in the format expected by the UI
            return {
                "analysis": formatted_result,
                "score": result.get("Total Score", 0),
                "category": result.get("Health Status", "Unknown")
            }
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def submit_realtime_health_score(health_data):
    """Submit health data for real-time health score analysis"""
    try:
        # Log the data being sent
        print(f"Submitting real-time health data: {json.dumps(health_data, indent=2)}")

        # Process health data to handle "Unknown" values
        processed_health_data = {}
        for key, value in health_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle test results with string values
            elif key in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]:
                processed_health_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    if isinstance(value, (int, float)):
                        processed_health_data[key] = value
                    else:
                        processed_health_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_health_data[key] = value

        payload = {
            "user_id": st.session_state.user_id,
            "health_data": processed_health_data,
            "treat_unknown_as_null": "true"
        }

        # Make the request
        response = requests.post(f"{API_URL}/realtime-health-score", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received real-time health score response: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def submit_kidney_function(kidney_data):
    """Submit kidney function data for analysis"""
    try:
        # Log the data being sent
        print(f"Submitting kidney function data: {json.dumps(kidney_data, indent=2)}")

        # Process kidney data to handle "Unknown" values
        processed_kidney_data = {}
        for key, value in kidney_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle string values for categorical fields
            elif key in ["Sex"]:
                processed_kidney_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    if isinstance(value, (int, float)):
                        processed_kidney_data[key] = value
                    else:
                        processed_kidney_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_kidney_data[key] = value

        payload = {
            "user_id": st.session_state.user_id,
            "kidney_data": processed_kidney_data
        }

        # Make the request
        response = requests.post(f"{API_URL}/kidney-function", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received response: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

# The function is kept as a fallback but will not be used in normal operation
def generate_kidney_recommendations(abnormal_params, kidney_data):
    """Generate personalized recommendations based on abnormal kidney function parameters"""
    recommendations = []

    # Specific recommendations based on abnormal parameters
    if "Serum Creatinine" in abnormal_params or "eGFR" in abnormal_params:
        recommendations.append("🔹 Consider reducing protein intake and consult with a nephrologist")
        recommendations.append("🔹 Monitor blood pressure regularly and keep it under control")

    if "Serum Sodium" in abnormal_params:
        if kidney_data.get("Serum Sodium", 0) > 145:
            recommendations.append("🔹 Reduce salt intake and increase water consumption")
        else:
            recommendations.append("🔹 Consult with your doctor about proper fluid intake")

    if "Serum Potassium" in abnormal_params:
        if kidney_data.get("Serum Potassium", 0) > 5.0:
            recommendations.append("🔹 Limit high-potassium foods like bananas, oranges, and potatoes")
        else:
            recommendations.append("🔹 Include potassium-rich foods in your diet as advised by your doctor")

    if "Serum Calcium" in abnormal_params:
        recommendations.append("🔹 Discuss vitamin D and calcium supplementation with your healthcare provider")

    if "Serum Uric Acid" in abnormal_params:
        recommendations.append("🔹 Limit purine-rich foods like red meat, seafood, and beer")
        recommendations.append("🔹 Increase intake of cherries and vitamin C-rich foods")

    if "ACR" in abnormal_params or "Urine Albumin" in abnormal_params:
        recommendations.append("🔹 Control blood sugar and blood pressure carefully")
        recommendations.append("🔹 Follow up with regular kidney function tests")

    # Format the recommendations as a list
    formatted_recs = "**Personalized Recommendations:**\n"
    for rec in recommendations:
        # Check if the recommendation already starts with a list marker
        if rec.strip().startswith("🔹"):
            # Remove the emoji and add a dash instead
            rec = rec.replace("🔹", "-", 1).strip()
            formatted_recs += f"{rec}\n"
        else:
            formatted_recs += f"- {rec}\n"

    return formatted_recs

def submit_lipid_profile(lipid_data):
    """Submit lipid profile data for analysis using the new tools_lipid_profile2 tool"""
    try:
        # Log the data being sent
        print(f"Submitting lipid profile data: {json.dumps(lipid_data, indent=2)}")

        # Process lipid data to handle "Unknown" values
        processed_lipid_data = {}
        for key, value in lipid_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle string values for categorical fields
            elif key in ["sex", "smoker", "hypertension", "diabetes", "family_history"]:
                processed_lipid_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    if isinstance(value, (int, float)):
                        processed_lipid_data[key] = value
                    else:
                        processed_lipid_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_lipid_data[key] = value

        payload = {
            "user_id": st.session_state.user_id,
            "lipid_data": processed_lipid_data
        }

        # Make the request
        response = requests.post(f"{API_URL}/lipid-profile", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received response: {json.dumps(result, indent=2)}")

            # Format the recommendations as a list if they're not already
            if "recommendations" in result and isinstance(result["recommendations"], list):
                formatted_recs = []
                for rec in result["recommendations"]:
                    if not rec.startswith("-"):
                        formatted_recs.append(f"- {rec}")
                    else:
                        formatted_recs.append(rec)
                result["formatted_recommendations"] = formatted_recs

            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def submit_liver_function(liver_data):
    """Submit liver function data for analysis"""
    try:
        # Log the data being sent
        print(f"Submitting liver function data: {json.dumps(liver_data, indent=2)}")

        payload = {
            "user_id": st.session_state.user_id,
            "lft_data": liver_data
        }

        # Make the request
        response = requests.post(f"{API_URL}/liver-function/manual", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received liver function response: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def submit_lung_capacity(spirometry_data):
    """Submit spirometry data for lung capacity analysis"""
    try:
        # Log the data being sent
        print(f"Submitting lung capacity data: {json.dumps(spirometry_data, indent=2)}")

        # Process spirometry data to handle "Unknown" values
        processed_spirometry_data = {}
        for key, value in spirometry_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle string values for categorical fields
            elif key in ["Sex", "Race", "Smoking_Status"]:
                processed_spirometry_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    if isinstance(value, (int, float)):
                        processed_spirometry_data[key] = value
                    else:
                        processed_spirometry_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_spirometry_data[key] = value

        payload = {
            "user_id": st.session_state.user_id,
            "spirometry_data": processed_spirometry_data
        }

        # Make the request
        response = requests.post(f"{API_URL}/lung-capacity", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received response: {json.dumps(result, indent=2)}")

            # Format the recommendations as a list if they're not already
            if "recommendations" in result and isinstance(result["recommendations"], list):
                formatted_recs = []
                for rec in result["recommendations"]:
                    if not rec.startswith("-"):
                        formatted_recs.append(f"- {rec}")
                    else:
                        formatted_recs.append(rec)
                result["formatted_recommendations"] = formatted_recs

            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def submit_test_results(test_data):
    """Submit test results for analysis (Malaria, Widal, or any custom lab test)"""
    try:
        # Log the data being sent
        print(f"Submitting test results data: {json.dumps(test_data, indent=2)}")

        # Check if this is a custom test request
        if "test_name" in test_data and "Custom Test" in test_data.get("selected_tests", []):
            # This is a custom lab test request, use the lab-test-explainer endpoint
            test_name = test_data["test_name"]
            print(f"Processing custom lab test request for: {test_name}")

            # Get lab test explanation
            lab_test_result = request_lab_test_explanation(test_name=test_name)

            if "error" in lab_test_result:
                return {"error": lab_test_result["error"]}

            # If test values were provided, add interpretation
            test_values = test_data.get("test_values")
            interpretation = []
            recommendations = []

            if test_values and lab_test_result.get("found", False):
                # Add basic interpretation based on normal ranges
                interpretation.append("Based on the test values you provided:")

                # Try to extract values and compare with normal ranges
                if "normal_ranges" in lab_test_result:
                    normal_ranges = lab_test_result["normal_ranges"]

                    # Simple parsing of test values (this is a basic implementation)
                    value_lines = test_values.strip().split('\n')
                    parsed_values = {}

                    for line in value_lines:
                        if ':' in line:
                            param, value_str = line.split(':', 1)
                            param = param.strip()
                            value_str = value_str.strip()

                            # Try to convert to float if possible
                            try:
                                # Remove any commas in numbers like 7,500
                                clean_value = value_str.replace(',', '')
                                # Extract just the numeric part if there are units
                                numeric_part = re.search(r'([\d\.]+)', clean_value)
                                if numeric_part:
                                    parsed_values[param] = float(numeric_part.group(1))
                                else:
                                    parsed_values[param] = value_str
                            except:
                                parsed_values[param] = value_str

                    # Compare with normal ranges
                    abnormal_values = []
                    for param, range_str in normal_ranges.items():
                        for user_param, user_value in parsed_values.items():
                            # Check if parameter names are similar
                            if param.lower() in user_param.lower() or user_param.lower() in param.lower():
                                if isinstance(user_value, (int, float)):
                                    # Try to extract numeric range
                                    range_match = re.search(r'([\d\.]+)-([\d\.]+)', range_str)
                                    if range_match:
                                        low = float(range_match.group(1))
                                        high = float(range_match.group(2))

                                        if user_value < low:
                                            abnormal_values.append(f"{user_param} ({user_value}) is below the normal range ({range_str})")
                                            recommendations.append(f"Discuss your low {user_param} level with your healthcare provider")
                                        elif user_value > high:
                                            abnormal_values.append(f"{user_param} ({user_value}) is above the normal range ({range_str})")
                                            recommendations.append(f"Discuss your high {user_param} level with your healthcare provider")
                                        else:
                                            interpretation.append(f"- {user_param} ({user_value}) is within the normal range ({range_str})")

                    # Add abnormal values to interpretation
                    for abnormal in abnormal_values:
                        interpretation.append(f"- {abnormal}")

                # Add general recommendations
                if not recommendations:
                    recommendations.append("Discuss your test results with your healthcare provider for a complete interpretation")
                    recommendations.append("Consider follow-up testing if recommended by your healthcare provider")

            # Create a doctor-like summary
            doctor_summary = f"I've analyzed your {test_name} results. "
            if lab_test_result.get("found", False):
                doctor_summary += "This test is commonly used to assess "
                if "what_it_measures" in lab_test_result and lab_test_result["what_it_measures"]:
                    doctor_summary += ", ".join(lab_test_result["what_it_measures"][:2])
                else:
                    doctor_summary += "important health parameters"
                doctor_summary += ". "

                if test_values:
                    if abnormal_values:
                        doctor_summary += f"I've noticed {len(abnormal_values)} values outside the normal range that we should discuss."
                    else:
                        doctor_summary += "Based on the values you provided, everything appears to be within normal ranges."
            else:
                doctor_summary += f"I don't have detailed information about this specific test in my database, but I'll provide what general information I can."

            # Combine lab test explanation with interpretation of user values
            result = {
                "doctor_summary": doctor_summary,
                "description": lab_test_result.get("description", f"Information about {test_name}"),
                "normal_ranges": lab_test_result.get("normal_ranges", {}),
                "interpretation": interpretation,
                "recommendations": recommendations,
                "patient_education": lab_test_result.get("patient_education", ""),
                "preparation": lab_test_result.get("preparation", ""),
                "when_ordered": lab_test_result.get("when_ordered", [])
            }

            return result

        # Process test data to handle "Unknown" values for standard tests
        processed_test_data = {}
        for key, value in test_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Keep all values as is since test results are typically categorical
            else:
                processed_test_data[key] = value

        payload = {
            "user_id": st.session_state.user_id,
            "test_results": processed_test_data
        }

        # Make the request for standard tests (Malaria, Widal)
        response = requests.post(f"{API_URL}/test-results", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received response: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def handle_confirmation(confirmation_type):
    """Handle user confirmation for different actions"""
    if confirmation_type == "vital_signs":
        st.session_state.waiting_for_vitals = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your vital signs below:"

    elif confirmation_type == "realtime_health_score":
        st.session_state.waiting_for_realtime_health_score = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your health data below for comprehensive health score analysis. This includes both vital signs and lifestyle factors:"

    elif confirmation_type == "kidney_function":
        st.session_state.waiting_for_kidney_function = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your kidney function test results below:"

    elif confirmation_type == "lipid_profile":
        st.session_state.waiting_for_lipid_profile = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your lipid profile test results below for comprehensive cardiac health planning:"

    elif confirmation_type == "lung_capacity":
        st.session_state.waiting_for_lung_capacity = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your spirometry test results below for lung capacity analysis:"

    elif confirmation_type == "liver_function":
        st.session_state.waiting_for_liver_function = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your liver function test data below:"

    elif confirmation_type == "test_results":
        st.session_state.waiting_for_test_results = True
        st.session_state.test_selection_step = True
        st.session_state.test_form_step = False
        st.session_state.malaria_form_step = False
        st.session_state.widal_form_step = False
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please select the type of test result you'd like to interpret:"

    elif confirmation_type == "symptom_checker":
        st.session_state.waiting_for_symptom_checker = True
        st.session_state.symptom_checker_step = 1
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please describe your symptoms in detail below. The more information you provide, the more accurate the analysis will be:"

    elif confirmation_type == "lab_test_explainer":
        st.session_state.waiting_for_lab_test_explainer = True
        st.session_state.lab_test_explainer_step = 1
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None

        return "Please enter the name of any lab test you'd like to understand or interpret. You can type any test name, such as Complete Blood Count (CBC), Lipid Profile, Thyroid Function Tests, or any other lab test you're curious about."

    elif confirmation_type == "chronic_tracker":
        st.session_state.waiting_for_chronic_tracker = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None

        # Show the condition selection form
        return "Please select the chronic condition you'd like to track and enter your data below:"

    elif confirmation_type == "health_consultation":
        # Reset confirmation state
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None

        # Request health consultation directly from the server
        return request_health_consultation()

    elif confirmation_type == "doc_summarizer":
        st.session_state.waiting_for_doc_summarizer = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please upload a medical document (PDF format) for summarization:"

    elif confirmation_type == "chronic_risk":
        st.session_state.waiting_for_chronic_risk = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your information for chronic disease risk prediction:"



    elif confirmation_type == "lifestyle_tracking":
        st.session_state.waiting_for_lifestyle_tracking = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your lifestyle habits for tracking and analysis:"

    elif confirmation_type == "progress_tracker":
        st.session_state.waiting_for_progress_tracker = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your vital signs to track your health progress over time:"

    elif confirmation_type == "weekly_digest":
        st.session_state.waiting_for_weekly_digest = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your current vital signs to generate a weekly health digest:"

    elif confirmation_type == "mental_health_assessment":
        st.session_state.waiting_for_mental_health_assessment = True
        st.session_state.mental_health_assessment_step = 1
        st.session_state.mental_health_assessment_data = {}
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "I'll guide you through a comprehensive mental health assessment. This assessment is confidential and will help me understand your current mental health status. Let's start with some basic information below:"

    return "I'm not sure what you're confirming. How can I help you?"

def fetch_chat_history():
    """Fetch chat history (including health data) from the MCP server."""
    try:
        response = requests.get(
            f"{API_URL}/chat-history",
            params={
                "user_id": st.session_state.user_id,
                "session_id": st.session_state.session_id
            },
            timeout=5
        )
        if response.status_code == 200:
            return response.json()  # Should return a dict with messages and possibly health data
        else:
            print(f"Failed to fetch chat history. Status code: {response.status_code}")
            return None
    except requests.RequestException as e:
        print(f"Error fetching chat history: {e}")
        return None

def generate_booking_url():
    """Generate a URL for booking a health consultation."""
    # Generate a unique booking ID
    booking_id = str(uuid.uuid4())

    # Create a booking URL with user ID and booking ID
    # This URL will allow the consultation service to fetch the user's data from the server
    booking_url = f"https://drdeucehealth.com/book-consultation?user_id={st.session_state.user_id}&booking_id={booking_id}"

    return booking_url

def request_device_recommendations():
    """Redirect users to TurboMedics website for device recommendations"""
    try:
        # Determine the test type for specific recommendations
        test_type = None

        # Check if we have a confirmation type set (most recent test)
        if st.session_state.confirmation_type:
            test_type = st.session_state.confirmation_type

        # Get test type for specific title
        test_type_name = {
            "vital_signs": "Vital Signs",
            "kidney_function": "Kidney Function",
            "lipid_profile": "Cardiovascular Health",
            "lung_capacity": "Respiratory Health",
            "liver_function": "Liver Health",
            "test_results": "Test Results",
            "realtime_health_score": "Health Score",
            "mental_health_assessment": "Mental Health"
        }.get(test_type, "Health")

        # TurboMedics URL
        turbomedics_url = "https://www.turbomedics.com/products"

        # Format the response with a redirect to TurboMedics
        if test_type:
            formatted_response = f"**Connected Health Device Recommendations for {test_type_name} Monitoring**\n\n"
            formatted_response += f"Based on your {test_type_name.lower()} data, I recommend visiting TurboMedics to explore their selection of health monitoring devices.\n\n"
        else:
            formatted_response = "**Connected Health Device Recommendations**\n\n"
            formatted_response += "I recommend visiting TurboMedics to explore their selection of health monitoring devices.\n\n"

        # Add clickable link to TurboMedics
        formatted_response += f"[**Click here to browse health monitoring devices at TurboMedics**]({turbomedics_url})\n\n"

        # Add additional context based on test type
        if test_type == "vital_signs":
            formatted_response += "TurboMedics offers a variety of devices for monitoring vital signs such as blood pressure monitors, pulse oximeters, and thermometers."
        elif test_type == "kidney_function":
            formatted_response += "TurboMedics offers devices that can help you monitor parameters related to kidney health, such as blood pressure monitors and urinalysis devices."
        elif test_type == "lipid_profile":
            formatted_response += "TurboMedics offers devices for cardiovascular health monitoring, including blood pressure monitors and cholesterol testing devices."
        elif test_type == "lung_capacity":
            formatted_response += "TurboMedics offers respiratory monitoring devices such as peak flow meters and pulse oximeters to help track your lung health."
        elif test_type == "liver_function":
            formatted_response += "TurboMedics offers devices for liver health monitoring, including blood pressure monitors and comprehensive health panels that can help track liver-related parameters."
        elif test_type == "realtime_health_score":
            formatted_response += "TurboMedics offers comprehensive health monitoring devices that can help you track multiple health parameters simultaneously."
        elif test_type == "mental_health_assessment":
            formatted_response += "TurboMedics offers devices for mental health and wellness monitoring, including stress monitors, sleep trackers, and heart rate variability devices that can help you track your mental wellness journey."
        else:
            formatted_response += "TurboMedics offers a wide range of health monitoring devices to help you track your health metrics at home."

        return formatted_response
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)

        # Simple error response with TurboMedics link
        error_response = "I'm sorry, there was an error processing your device recommendations request.\n\n"
        error_response += "You can still visit TurboMedics directly to explore their selection of health monitoring devices:\n\n"
        error_response += "[**Browse health monitoring devices at TurboMedics**](https://www.turbomedics.com/products)"

        return error_response

def submit_chronic_tracker(condition_data, tracking_frequency="as_needed", measurement_date=None):
    """Submit chronic condition tracking data for analysis with support for daily/weekly tracking"""
    try:
        # Log the data being sent
        print(f"Submitting chronic tracker data: {json.dumps(condition_data, indent=2)}")
        print(f"Tracking frequency: {tracking_frequency}")
        print(f"Measurement date: {measurement_date}")

        # Prepare payload for the server
        payload = {
            "user_id": st.session_state.user_id,
            "condition_data": condition_data,
            "tracking_frequency": tracking_frequency
        }

        # Add measurement date if provided
        if measurement_date:
            payload["measurement_date"] = measurement_date

        # Make the request to the track-chronic-condition endpoint
        response = requests.post(f"{API_URL}/track-chronic-condition", json=payload, timeout=5)
        print(f"Sent request to {API_URL}/track-chronic-condition with payload: {json.dumps(payload, indent=2)}")

        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            print(f"Received result from server: {json.dumps(result, indent=2)}")
            return result
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)

            # Generate a fallback response with the data we have
            condition_type = condition_data.get("condition_type", "chronic condition")
            print(f"Generating fallback response for condition type: {condition_type}")

            # Create a basic summary based on the condition type
            if condition_type == "diabetes":
                summary = "Based on your diabetes tracking data, I can see you've entered information about your glucose levels and symptoms."
                recommendations = [
                    "Monitor your blood glucose regularly",
                    "Follow your medication regimen as prescribed",
                    "Maintain a balanced diet rich in vegetables and whole grains",
                    "Stay physically active with regular exercise",
                    "Schedule regular check-ups with your healthcare provider"
                ]
            elif condition_type == "hypertension":
                summary = "Based on your hypertension tracking data, I can see you've entered information about your blood pressure readings."
                recommendations = [
                    "Monitor your blood pressure regularly",
                    "Take medications as prescribed",
                    "Reduce sodium intake in your diet",
                    "Engage in regular physical activity",
                    "Practice stress management techniques"
                ]
            else:
                summary = f"Thank you for tracking your {condition_type.replace('_', ' ')}. Regular monitoring is an important part of managing chronic conditions."
                recommendations = [
                    "Continue tracking your symptoms and measurements",
                    "Take medications as prescribed by your healthcare provider",
                    "Maintain a healthy lifestyle with proper diet and exercise",
                    "Schedule regular check-ups with your healthcare provider",
                    "Report any significant changes in your condition promptly"
                ]

            return {
                "error": error_msg,
                "summary": summary,
                "recommendations": recommendations,
                "current_analysis": {},
                "trend_analysis": {}
            }
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)

        # Generate a fallback response with the data we have
        condition_type = condition_data.get("condition_type", "chronic condition")
        return {
            "error": error_msg,
            "summary": f"I couldn't connect to the health server for your {condition_type} tracking, but I can still provide some general recommendations.",
            "recommendations": [
                "Continue monitoring your condition regularly",
                "Follow your prescribed treatment plan",
                "Maintain a healthy lifestyle with proper diet and exercise",
                "Consult with your healthcare provider for personalized advice"
            ],
            "current_analysis": {},
            "trend_analysis": {}
        }
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {
            "error": error_msg,
            "summary": "An unexpected error occurred, but I can still provide some general recommendations.",
            "recommendations": [
                "Continue monitoring your condition regularly",
                "Follow your prescribed treatment plan",
                "Maintain a healthy lifestyle with proper diet and exercise",
                "Consult with your healthcare provider for personalized advice"
            ],
            "current_analysis": {},
            "trend_analysis": {}
        }

def submit_symptoms(symptoms, age=None, sex=None, duration=None, severity="moderate"):
    """Submit symptoms to the server for analysis"""
    try:
        # Create payload
        payload = {
            "user_id": st.session_state.user_id,
            "symptoms": symptoms,
            "age": age,
            "sex": sex,
            "duration": duration,
            "severity": severity
        }

        # Log the data being sent
        print(f"Submitting symptoms: {json.dumps(payload, indent=2)}")

        # Make the request
        response = requests.post(f"{API_URL}/symptom-checker", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received symptom analysis: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def request_followup_reminder():
    """Generate follow-up reminders specific to the most recent test performed"""
    try:
        # Import the followup reminder tool directly
        try:
            sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))
            from tools.tools_followup_reminder import followup_reminder_tool
            print("Successfully imported followup_reminder_tool")
        except ImportError as e:
            print(f"Failed to import tools: {e}")
            return f"I'm sorry, I couldn't access the follow-up reminder tool: {str(e)}\n\nPlease try again later."

        # Get the most recent test data
        most_recent_test_data = {}
        most_recent_test_type = ""

        # Check if any test was recently performed
        if st.session_state.confirmation_type:
            test_type = st.session_state.confirmation_type
            most_recent_test_type = test_type

            # Get health data from the server for this specific test
            try:
                # Try to get user health data from the server
                response = requests.get(f"{API_URL}/user-health-data/{st.session_state.user_id}", timeout=10)

                if response.status_code == 200:
                    user_health_data = response.json()
                    print(f"Retrieved user health data: {json.dumps(user_health_data, indent=2)}")

                    # Extract only the data for the specific test type
                    if test_type == "vital_signs" and "vital_signs" in user_health_data:
                        most_recent_test_data = user_health_data["vital_signs"]
                    elif test_type == "kidney_function" and "kidney_function" in user_health_data:
                        most_recent_test_data = user_health_data["kidney_function"]
                    elif test_type == "lipid_profile" and "lipid_profile" in user_health_data:
                        most_recent_test_data = user_health_data["lipid_profile"]
                    elif test_type == "lung_capacity" and "lung_capacity" in user_health_data:
                        most_recent_test_data = user_health_data["lung_capacity"]
                    elif test_type == "liver_function" and "liver_function" in user_health_data:
                        most_recent_test_data = user_health_data["liver_function"]
                    elif test_type == "test_results" and "test_results" in user_health_data:
                        most_recent_test_data = user_health_data["test_results"]
                    elif test_type == "realtime_health_score" and "health_score" in user_health_data:
                        most_recent_test_data = user_health_data["health_score"]
                    elif test_type == "chronic_tracker" and "chronic_tracker" in user_health_data:
                        # For chronic tracker, get the most recent entry
                        if user_health_data["chronic_tracker"]:
                            most_recent_entry = user_health_data["chronic_tracker"][-1]
                            if "data" in most_recent_entry:
                                most_recent_test_data = most_recent_entry["data"]
                else:
                    print(f"Could not retrieve user health data: {response.status_code}")
            except Exception as e:
                print(f"Error retrieving user health data: {str(e)}")

        # If no specific test data is available, check if we have any test data in session state
        if not most_recent_test_data:
            if hasattr(st.session_state, 'vital_signs_data') and st.session_state.vital_signs_data:
                most_recent_test_data = st.session_state.vital_signs_data
                most_recent_test_type = "vital_signs"
            elif hasattr(st.session_state, 'kidney_function_data') and st.session_state.kidney_function_data:
                most_recent_test_data = st.session_state.kidney_function_data
                most_recent_test_type = "kidney_function"
            elif hasattr(st.session_state, 'lipid_profile_data') and st.session_state.lipid_profile_data:
                most_recent_test_data = st.session_state.lipid_profile_data
                most_recent_test_type = "lipid_profile"
            elif hasattr(st.session_state, 'lung_capacity_data') and st.session_state.lung_capacity_data:
                most_recent_test_data = st.session_state.lung_capacity_data
                most_recent_test_type = "lung_capacity"
            elif hasattr(st.session_state, 'liver_function_data') and st.session_state.liver_function_data:
                most_recent_test_data = st.session_state.liver_function_data
                most_recent_test_type = "liver_function"
            elif hasattr(st.session_state, 'test_results_data') and st.session_state.test_results_data:
                most_recent_test_data = st.session_state.test_results_data
                most_recent_test_type = "test_results"
            elif hasattr(st.session_state, 'health_score_data') and st.session_state.health_score_data:
                most_recent_test_data = st.session_state.health_score_data
                most_recent_test_type = "realtime_health_score"

        # If still no test data, return a message
        if not most_recent_test_data:
            return "No recent test data found. Please perform a health test first to get follow-up reminders."

        # Add test type to the data
        most_recent_test_data["test_type"] = most_recent_test_type

        # Prepare input data for the tool
        input_data = {
            "user_id": st.session_state.user_id,
            "health_data": most_recent_test_data
        }

        # Add test_type from session state if available
        if hasattr(st.session_state, 'followup_context') and st.session_state.followup_context:
            input_data["test_type"] = st.session_state.followup_context
            print(f"Using test_type from session state: {st.session_state.followup_context}")

        # Call the followup reminder tool directly
        print(f"Calling followup_reminder_tool with input: {json.dumps(input_data)}")
        try:
            tool_result = followup_reminder_tool(json.dumps(input_data))
            print(f"Received raw result from followup_reminder_tool: {tool_result}")
            result = json.loads(tool_result)
            print(f"Parsed result: {json.dumps(result)}")
        except Exception as e:
            print(f"Error calling followup_reminder_tool: {str(e)}")
            return f"I'm sorry, there was an error generating your follow-up reminders: {str(e)}\n\nPlease try again later."

        # Get the formatted summary from the tool
        if "summary" in result:
            formatted_response = result["summary"]
        else:
            formatted_response = "No follow-up reminders needed for this test. All your results are within normal ranges."

        # Add test type information
        test_type_name = {
            "vital_signs": "Vital Signs",
            "kidney_function": "Kidney Function",
            "lipid_profile": "Lipid Profile",
            "lung_capacity": "Lung Capacity",
            "liver_function": "Liver Function",
            "test_results": "Test Results",
            "realtime_health_score": "Health Score",
            "chronic_tracker": "Chronic Condition"
        }.get(most_recent_test_type, "Recent Test")

        if formatted_response != "No follow-up reminders needed for this test. All your results are within normal ranges.":
            formatted_response = f"Follow-up reminders for your {test_type_name} test:\n\n{formatted_response}"
        else:
            formatted_response = f"No follow-up reminders needed for your {test_type_name} test. All your results are within normal ranges."

        return formatted_response
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)

        return f"I'm sorry, there was an unexpected error: {error_msg}\n\nPlease try again later."

def request_lab_test_explanation(test_name=None, list_tests=False):
    """Request lab test explanation from the server"""
    try:
        # Create payload
        payload = {
            "user_id": st.session_state.user_id,
            "test_name": test_name,
            "list_tests": list_tests
        }

        # Log the data being sent
        print(f"Requesting lab test explanation: {json.dumps(payload, indent=2)}")

        # Make the request
        response = requests.post(f"{API_URL}/lab-test-explainer", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received lab test explanation: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def request_health_consultation():
    """Request a comprehensive health consultation from the server"""
    try:
        # Make the request to the health consultation endpoint
        # Add a parameter to indicate that "Unknown" values should be treated as null
        response = requests.get(
            f"{API_URL}/health-consultation",
            params={
                "user_id": st.session_state.user_id,
                "treat_unknown_as_null": "true"
            },
            timeout=10
        )

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received health consultation response: {json.dumps(result, indent=2)}")

            # Format the consultation response
            formatted_response = ""

            # Use the formatted consultation text from the server
            if "consultation" in result:
                formatted_response = result["consultation"]
            else:
                formatted_response = "Error: No consultation data received from the server."

            # Add appointment recommendations if available
            has_appointments = False
            if "appointment_recommendations" in result and result["appointment_recommendations"]:
                has_appointments = True
                formatted_response += "\n\n**Recommended Appointments:**\n"
                for appointment in result["appointment_recommendations"]:
                    urgency = "🔴" # Default high urgency
                    if "urgency_levels" in result and appointment in result["urgency_levels"]:
                        urgency = "🟢" if result["urgency_levels"][appointment] == "Low" else "🟡" if result["urgency_levels"][appointment] == "Medium" else "🔴"

                    reason = ""
                    if "appointment_reasons" in result and appointment in result["appointment_reasons"]:
                        reason = f": {result['appointment_reasons'][appointment]}"

                    formatted_response += f"{urgency} **{appointment}**{reason}\n"

            # Add recommended tests if available
            if "recommended_tests" in result and result["recommended_tests"]:
                has_appointments = True
                formatted_response += "\n\n**Recommended Tests and Screenings:**\n"
                for test in result["recommended_tests"]:
                    formatted_response += f"• {test}\n"

            # Add follow-up schedule if available
            if "follow_up_schedule" in result and result["follow_up_schedule"]:
                formatted_response += "\n\n**Follow-up Schedule:**\n"
                for check, timeframe in result["follow_up_schedule"].items():
                    formatted_response += f"• {check}: {timeframe}\n"

            # Add care coordination if available
            if "care_coordination" in result and result["care_coordination"]:
                formatted_response += "\n\n**Proactive Care Coordination:**\n"
                for care in result["care_coordination"]:
                    formatted_response += f"• {care}\n"

            # Add booking link if there are recommended appointments or tests
            if has_appointments:
                # Generate a booking URL
                booking_url = generate_booking_url()

                formatted_response += f"\n\n**Would you like to schedule these appointments?**\n\n"
                formatted_response += f"[**Click here to book your appointments**]({booking_url})\n\n"
                formatted_response += "Your health data will be shared with the healthcare provider to ensure you receive appropriate care."

            # Add doctor visit recommendation if available
            doctor_visit = result.get("doctor_visit_recommended", False)
            urgency_level = result.get("urgency_level", "Low")

            if doctor_visit and urgency_level == "High":
                formatted_response += "\n\n🔴 **Based on your health data, I strongly recommend seeing a doctor as soon as possible.**"
            elif doctor_visit and urgency_level == "Medium":
                formatted_response += "\n\n🟡 **Based on your health data, I recommend scheduling a doctor's appointment in the next few weeks.**"
            elif doctor_visit:
                formatted_response += "\n\n🟢 **Based on your health data, I recommend scheduling a routine check-up with your doctor.**"

            return formatted_response
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")

            # Generate a booking URL even in case of error
            booking_url = generate_booking_url()

            error_response = f"I'm sorry, there was an error processing your health consultation: {error_msg}\n\n"
            error_response += "However, you can still book an appointment with a healthcare professional who can help you:\n\n"
            error_response += f"[**Click here to book an appointment**]({booking_url})\n\n"
            error_response += "Your health data will be shared with the healthcare provider to ensure you receive appropriate care."

            return error_response
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)

            # Generate a booking URL even in case of error
            booking_url = generate_booking_url()

            error_response = f"I'm sorry, there was an error processing your health consultation: {error_msg}\n\n"
            error_response += "However, you can still book an appointment with a healthcare professional who can help you:\n\n"
            error_response += f"[**Click here to book an appointment**]({booking_url})\n\n"
            error_response += "Your health data will be shared with the healthcare provider to ensure you receive appropriate care."

            return error_response
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)

        # Generate a booking URL even in case of error
        booking_url = generate_booking_url()

        error_response = f"I'm sorry, there was an error connecting to the server: {error_msg}\n\n"
        error_response += "However, you can still book an appointment with a healthcare professional who can help you:\n\n"
        error_response += f"[**Click here to book an appointment**]({booking_url})\n\n"
        error_response += "Your health data will be shared with the healthcare provider to ensure you receive appropriate care."

        return error_response
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)

        # Generate a booking URL even in case of error
        booking_url = generate_booking_url()

        error_response = f"I'm sorry, there was an unexpected error: {error_msg}\n\n"
        error_response += "However, you can still book an appointment with a healthcare professional who can help you:\n\n"
        error_response += f"[**Click here to book an appointment**]({booking_url})\n\n"
        error_response += "Your health data will be shared with the healthcare provider to ensure you receive appropriate care."

        return error_response


# === SIDEBAR ===
with st.sidebar:
    # Server status at the top of the sidebar
    status_text, status_data = check_server_status()
    st.session_state.server_status = status_text

    st.markdown(f"### Agent Server: {st.session_state.server_status}")

    # User ID
    st.markdown(f"**User ID**: {st.session_state.user_id[:8]}...")

    # Model selection
    selected_model = st.selectbox(
        "Choose Model",
        MODELS,
        index=MODELS.index(DEFAULT_MODEL) if DEFAULT_MODEL in MODELS else 0
    )

    # Chat title
    st.markdown("### Chat")
    st.markdown(f"**Title**: {st.session_state.chat_title}")

    # New chat button
    if st.button("New Chat", key="new_chat"):
        # Reset session state
        st.session_state.session_id = str(uuid.uuid4())
        st.session_state.message_log = [{"role": "ai", "content": "Hi! I'm Dr. Deuce. How can I help you today?"}]
        st.session_state.chat_title = "New Chat"
        st.session_state.waiting_for_vitals = False
        st.session_state.waiting_for_realtime_health_score = False
        st.session_state.waiting_for_kidney_function = False
        st.session_state.waiting_for_lipid_profile = False
        st.session_state.waiting_for_lung_capacity = False
        st.session_state.waiting_for_test_results = False
        st.session_state.current_test_type = "Malaria Test"
        st.session_state.test_selection_step = False
        st.session_state.test_form_step = False
        st.session_state.malaria_form_step = False
        st.session_state.widal_form_step = False
        st.session_state.waiting_for_confirmation = False
        st.session_state.waiting_for_health_consultation = False
        st.session_state.waiting_for_device_recommendation = False
        st.session_state.waiting_for_symptom_checker = False
        st.session_state.symptom_checker_step = 1
        st.session_state.waiting_for_lab_test_explainer = False
        st.session_state.lab_test_explainer_step = 1
        st.session_state.waiting_for_chronic_tracker = False
        st.session_state.chronic_condition_type = "diabetes"
        st.session_state.chronic_tracker_data = {}
        st.session_state.confirmation_type = None

        # Reset new tool states
        st.session_state.waiting_for_doc_summarizer = False
        st.session_state.waiting_for_chronic_risk = False
        st.session_state.waiting_for_lifestyle_tracking = False
        st.session_state.chronic_risk_data = {}
        st.session_state.lifestyle_habits_data = {}

        # Health data is preserved on the server without notification

        st.rerun()



# === MAIN CONTENT ===
st.title("🩺 Dr. Deuce Health Assistant")
st.caption("Your AI Assistant for Healthcare Issues")

# Quick action buttons above the chat
st.markdown("### Quick Actions")

# Create 3 rows with 4 columns each for better organization
st.markdown('<div class="category-header">Health Assessment</div>', unsafe_allow_html=True)
assessment_col1, assessment_col2, assessment_col3, assessment_col4 = st.columns(4)

with assessment_col1:
    st.markdown('<div class="assessment-btn">', unsafe_allow_html=True)
    if st.button("📊 Comprehensive Health Score", key="btn_realtime_health_score"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "realtime_health_score"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to generate a comprehensive health score that includes both vital signs and lifestyle factors? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with assessment_col2:
    st.markdown('<div class="assessment-btn">', unsafe_allow_html=True)
    if st.button("💓 Vital Signs Monitor", key="btn_vitals"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "vital_signs"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to enter your vital signs for monitoring? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with assessment_col3:
    st.markdown('<div class="assessment-btn">', unsafe_allow_html=True)
    if st.button("🩸 Lab Test Interpreter", key="btn_lab_test_explainer"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "lab_test_explainer"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to learn about different lab tests, what they measure, and their normal ranges? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with assessment_col4:
    st.markdown('<div class="assessment-btn">', unsafe_allow_html=True)
    if st.button("🔍 Malaria & Widal Test Analysis", key="btn_test_results"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "test_results"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to interpret your Malaria or Widal test results? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

# Specialized Analysis Row
st.markdown('<div class="category-header">Specialized Analysis</div>', unsafe_allow_html=True)
specialized_col1, specialized_col2, specialized_col3, specialized_col4 = st.columns(4)

with specialized_col1:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("🫀 Lipid Profile Analyzer", key="btn_lipid_profile"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "lipid_profile"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to analyze your lipid profile for cardiac health planning? This will include ASCVD risk assessment and personalized diet recommendations. Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with specialized_col2:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("🫁 Lung Capacity Analyzer", key="btn_lung_capacity"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "lung_capacity"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to analyze your lung capacity test? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with specialized_col3:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("🫘 Liver Function Analyzer", key="btn_liver_function"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "liver_function"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to analyze your liver function? This will include liver enzyme analysis, hepatic health assessment, and personalized recommendations. Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with specialized_col3:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("🫘 Kidney Function Analysis", key="btn_kidney_function"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "kidney_function"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to analyze your kidney function? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with specialized_col4:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("🔄 Chronic Condition Tracker", key="btn_chronic_tracker"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "chronic_tracker"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to track your chronic condition and get personalized feedback? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

# Advanced Tools Row
st.markdown('<div class="category-header">Advanced Tools</div>', unsafe_allow_html=True)
advanced_col1, advanced_col2, advanced_col3, advanced_col4 = st.columns(4)

with advanced_col1:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("🔮 Chronic Disease Risk", key="btn_chronic_risk"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "chronic_risk"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to predict your risk of developing chronic diseases? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with advanced_col2:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("🥗 Lifestyle Coach", key="btn_lifestyle_tracking"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "lifestyle_tracking"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to track your lifestyle habits and get personalized coaching? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with advanced_col3:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("🧠 Mental Health Assessment", key="btn_mental_health"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "mental_health_assessment"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to complete a comprehensive mental health assessment? This includes stress/burnout evaluation, depression screening (PHQ-9), anxiety screening (GAD-7), and AI-powered risk prediction. Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with advanced_col4:
    st.markdown('<div class="analysis-btn">', unsafe_allow_html=True)
    if st.button("📈 Progress Tracker", key="btn_progress_tracker"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "progress_tracker"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to track your health progress over time and see trend analysis? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

# Support & Assistance Row
st.markdown('<div class="category-header">Support & Assistance</div>', unsafe_allow_html=True)
support_col1, support_col2, support_col3, support_col4 = st.columns(4)

with support_col1:
    st.markdown('<div class="support-btn">', unsafe_allow_html=True)
    if st.button("🩺 Health Consultation", key="btn_consultation"):
        # Add user message to chat history
        st.session_state.message_log.append({"role": "user", "content": "I'd like a health consultation"})

        # Request health consultation directly from the server
        consultation_response = request_health_consultation()

        # Check if the response contains a message about insufficient data
        if "insufficient data" in consultation_response.lower():
            # Add a more helpful message suggesting what data to enter
            consultation_response += "\n\nTo get a more comprehensive health consultation, please consider entering your data using the quick action buttons above:\n\n"
            consultation_response += "- **Health Score**: For a comprehensive health assessment\n"
            consultation_response += "- **Vital Signs**: To track your basic health metrics\n"
            consultation_response += "- **Kidney Function**: For renal health assessment\n"
            consultation_response += "- **Lipid Profile Analyzer**: For cardiovascular risk assessment\n"
            consultation_response += "- **Lung Capacity Analyzer**: For respiratory health evaluation\n"

        # Add the response to chat history
        st.session_state.message_log.append({"role": "ai", "content": consultation_response})

        # Refresh the UI
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with support_col2:
    st.markdown('<div class="support-btn">', unsafe_allow_html=True)
    if st.button("🔎 Symptom Checker", key="btn_symptom_checker"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "symptom_checker"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to use the Smart Symptom Checker to analyze your symptoms and get potential diagnoses, recommended tests, and self-care advice? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with support_col3:
    st.markdown('<div class="support-btn">', unsafe_allow_html=True)
    if st.button("⏰ Follow-up Reminders", key="btn_followup_reminder"):
        # Add user message to chat history
        st.session_state.message_log.append({"role": "user", "content": "I'd like to see follow-up reminders for my most recent test"})

        # Request follow-up reminders for the most recent test
        reminder_response = request_followup_reminder()

        # Add the response to chat history
        st.session_state.message_log.append({"role": "ai", "content": reminder_response})

        # Refresh the UI
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with support_col4:
    st.markdown('<div class="support-btn">', unsafe_allow_html=True)
    if st.button("📄 Document Summarizer", key="btn_doc_summarizer"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "doc_summarizer"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to upload a medical document for summarization? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

# Health Insights Row
st.markdown('<div class="category-header">Health Insights</div>', unsafe_allow_html=True)
insights_col1, insights_col2, insights_col3, insights_col4 = st.columns(4)

with insights_col1:
    st.markdown('<div class="insights-btn">', unsafe_allow_html=True)
    if st.button("📊 Weekly Health Digest", key="btn_weekly_digest"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "weekly_digest"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to generate a weekly digest of your health data? Type 'yes' to begin."})
        st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)

with insights_col2, insights_col3, insights_col4:
    # Empty columns for balance
    pass





# Chat history
st.markdown("### Chat History")
chat_container = st.container()

with chat_container:
    for message in st.session_state.message_log:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])



# Progress Tracker form (only shown when waiting for progress tracker)
if st.session_state.waiting_for_progress_tracker:
    with st.form(key="progress_tracker_form"):
        st.markdown("### Health Progress Tracker")
        st.markdown("Enter your vital signs to track your health progress over time and see trend analysis.")

        col1, col2 = st.columns(2)

        with col1:
            # Blood Pressure (Systolic)
            bp_systolic = st.number_input("Blood Pressure (Systolic)", min_value=70, max_value=200, value=120, key="progress_bp_sys")

            # Blood Pressure (Diastolic)
            bp_diastolic = st.number_input("Blood Pressure (Diastolic)", min_value=40, max_value=120, value=80, key="progress_bp_dia")

            # Heart Rate
            heart_rate = st.number_input("Heart Rate (bpm)", min_value=40, max_value=200, value=75, key="progress_hr")

            # Weight (for BMI calculation)
            weight = st.number_input("Weight (kg)", min_value=30.0, max_value=200.0, value=70.0, step=0.1, key="progress_weight")

        with col2:
            # Glucose
            glucose = st.number_input("Glucose (mg/dL)", min_value=50, max_value=300, value=100, key="progress_glucose")

            # SpO2
            spo2 = st.number_input("SpO2 (%)", min_value=80, max_value=100, value=98, key="progress_spo2")

            # Temperature
            temperature = st.number_input("Temperature (°C)", min_value=35.0, max_value=42.0, value=36.8, step=0.1, key="progress_temp")

            # Height (for BMI calculation)
            height = st.number_input("Height (cm)", min_value=100.0, max_value=220.0, value=170.0, step=0.1, key="progress_height")

        # Calculate BMI
        bmi = weight / ((height / 100) ** 2) if height > 0 else 0
        st.info(f"Calculated BMI: {bmi:.1f}")

        submit_button = st.form_submit_button(label="Track Progress")

        if submit_button:
            # Combine all vital signs data
            vital_signs = {
                "Blood Pressure (Systolic)": bp_systolic,
                "Blood Pressure (Diastolic)": bp_diastolic,
                "ECG (Heart Rate)": heart_rate,
                "Weight (BMI)": bmi,
                "Glucose": glucose,
                "SpO2": spo2,
                "Temperature": temperature
            }

            # Submit data for progress tracking
            result = track_progress(vital_signs)

            if "error" in result:
                response = f"Error tracking progress: {result['error']}"
            else:
                # Format the response
                response = "**Health Progress Tracking**\n\n"

                if "monthly_summary" in result and "trend_analysis" in result["monthly_summary"]:
                    response += "**Monthly Trend Analysis:**\n"
                    for param, data in result["monthly_summary"]["trend_analysis"].items():
                        trend_icon = data.get("trend", "→")
                        avg = data.get("avg", "N/A")
                        min_val = data.get("min", "N/A")
                        max_val = data.get("max", "N/A")
                        response += f"- {param}: {trend_icon} (Avg: {avg}, Range: {min_val}-{max_val})\n"
                    response += "\n"

                if "recommendations" in result:
                    response += "**Personalized Recommendations:**\n"
                    for rec in result["recommendations"]:
                        response += f"- {rec}\n"

            # Save the result for display
            st.session_state.progress_tracker_results = result

            # Add to chat history
            st.session_state.message_log.append({"role": "user", "content": "I've submitted my vital signs for progress tracking."})
            st.session_state.message_log.append({"role": "ai", "content": response})

            # Reset the form state
            st.session_state.waiting_for_progress_tracker = False

            # Rerun to update UI
            st.rerun()

# Real-time health score form (only shown when waiting for real-time health score)
if st.session_state.waiting_for_realtime_health_score:
    # Get default health data from the server
    default_data = get_default_health_data()

    with st.form(key="realtime_health_score_form"):
        st.markdown("### Enter Your Health Data for Real-Time Analysis")

        # Create tabs for different categories of health data
        vital_tab, lifestyle_tab, test_tab = st.tabs(["Vital Signs", "Lifestyle Factors", "Test Results"])

        # Helper function to create options for numeric fields
        def create_numeric_options(start, end, step=1, default=None):
            options = [f"{i:.1f}" if step < 1 else str(i) for i in np.arange(start, end + step, step)]
            options.append("N/A")
            # Set default index
            if default is None or default == "N/A":
                default_index = len(options) - 1  # N/A is the last option
            else:
                # Find the closest value to default
                closest_val = min(options[:-1], key=lambda x: abs(float(x) - float(default)))
                default_index = options.index(closest_val)
            return options, default_index

        # Vital Signs Tab
        with vital_tab:
            col1, col2 = st.columns(2)

            with col1:
                # BMI input with N/A option
                bmi_options, bmi_default_index = create_numeric_options(10.0, 50.0, 0.1, "24.2")
                bmi_selected = st.selectbox("BMI Value (normal range: 18.5-24.9)",
                                          options=bmi_options,
                                          index=bmi_default_index,
                                          key="rt_bmi_select")
                bmi = None if bmi_selected == "N/A" else float(bmi_selected)

                # Blood Pressure (Systolic) with N/A option
                bp_sys_default = "120" if default_data.get("Blood Pressure (Systolic)") is None else str(default_data.get("Blood Pressure (Systolic)"))
                bp_sys_options, bp_sys_default_index = create_numeric_options(70, 200, 1, bp_sys_default)
                bp_sys_selected = st.selectbox("Blood Pressure (Systolic)",
                                             options=bp_sys_options,
                                             index=bp_sys_default_index,
                                             key="rt_bp_sys_select")
                blood_pressure_systolic = None if bp_sys_selected == "N/A" else float(bp_sys_selected)

                # Blood Pressure (Diastolic) with N/A option
                bp_dia_default = "80" if default_data.get("Blood Pressure (Diastolic)") is None else str(default_data.get("Blood Pressure (Diastolic)"))
                bp_dia_options, bp_dia_default_index = create_numeric_options(40, 120, 1, bp_dia_default)
                bp_dia_selected = st.selectbox("Blood Pressure (Diastolic)",
                                             options=bp_dia_options,
                                             index=bp_dia_default_index,
                                             key="rt_bp_dia_select")
                blood_pressure_diastolic = None if bp_dia_selected == "N/A" else float(bp_dia_selected)

                # Heart Rate with N/A option
                hr_default = "75" if default_data.get("ECG (Heart Rate)") is None else str(default_data.get("ECG (Heart Rate)"))
                hr_options, hr_default_index = create_numeric_options(40, 200, 1, hr_default)
                hr_selected = st.selectbox("Heart Rate (bpm)",
                                         options=hr_options,
                                         index=hr_default_index,
                                         key="rt_hr_select")
                heart_rate = None if hr_selected == "N/A" else float(hr_selected)

            with col2:
                # Glucose with N/A option
                glucose_default = "100" if default_data.get("Glucose") is None else str(default_data.get("Glucose"))
                glucose_options, glucose_default_index = create_numeric_options(50, 300, 1, glucose_default)
                glucose_selected = st.selectbox("Glucose (mg/dL)",
                                              options=glucose_options,
                                              index=glucose_default_index,
                                              key="rt_glucose_select")
                glucose = None if glucose_selected == "N/A" else float(glucose_selected)

                # SpO2 with N/A option
                spo2_default = "98" if default_data.get("SpO2") is None else str(default_data.get("SpO2"))
                spo2_options, spo2_default_index = create_numeric_options(80, 100, 1, spo2_default)
                spo2_selected = st.selectbox("SpO2 (%)",
                                           options=spo2_options,
                                           index=spo2_default_index,
                                           key="rt_spo2_select")
                spo2 = None if spo2_selected == "N/A" else float(spo2_selected)

                # Temperature with N/A option
                temp_default = "36.8" if default_data.get("Temperature") is None else str(default_data.get("Temperature"))
                temp_options, temp_default_index = create_numeric_options(35.0, 42.0, 0.1, temp_default)
                temp_selected = st.selectbox("Temperature (°C)",
                                           options=temp_options,
                                           index=temp_default_index,
                                           key="rt_temp_select")
                temperature = None if temp_selected == "N/A" else float(temp_selected)

                # Respiratory Rate with N/A option
                resp_options, resp_default_index = create_numeric_options(8, 30, 1, "16")
                resp_selected = st.selectbox("Respiratory Rate (breaths/min)",
                                          options=resp_options,
                                          index=resp_default_index,
                                          key="rt_resp_select")
                respiratory_rate = None if resp_selected == "N/A" else float(resp_selected)

        # Lifestyle Factors Tab
        with lifestyle_tab:
            col1, col2 = st.columns(2)

            with col1:
                # Exercise with N/A option
                exercise_options, exercise_default_index = create_numeric_options(0, 7, 1, "3")
                exercise_selected = st.selectbox("Exercise (days/week)",
                                               options=exercise_options,
                                               index=exercise_default_index,
                                               key="rt_exercise_select")
                exercise = None if exercise_selected == "N/A" else float(exercise_selected)

                # Diet score with N/A option
                diet_options, diet_default_index = create_numeric_options(1, 5, 1, "3")
                diet_selected = st.selectbox("Diet Quality (1-5 scale)",
                                           options=diet_options,
                                           index=diet_default_index,
                                           key="rt_diet_select")
                diet = None if diet_selected == "N/A" else float(diet_selected)

                # Sleep with N/A option
                sleep_options, sleep_default_index = create_numeric_options(4, 12, 0.5, "7")
                sleep_selected = st.selectbox("Sleep (hours/night)",
                                            options=sleep_options,
                                            index=sleep_default_index,
                                            key="rt_sleep_select")
                sleep = None if sleep_selected == "N/A" else float(sleep_selected)

                # Stress with N/A option
                stress_options, stress_default_index = create_numeric_options(1, 5, 1, "2")
                stress_selected = st.selectbox("Stress Level (1-5 scale)",
                                             options=stress_options,
                                             index=stress_default_index,
                                             key="rt_stress_select")
                stress = None if stress_selected == "N/A" else float(stress_selected)

            with col2:
                # Hydration with N/A option
                hydration_options, hydration_default_index = create_numeric_options(0, 20, 1, "8")
                hydration_selected = st.selectbox("Hydration (cups of water/day)",
                                                options=hydration_options,
                                                index=hydration_default_index,
                                                key="rt_hydration_select")
                hydration = None if hydration_selected == "N/A" else float(hydration_selected)

                # Smoking with N/A option
                smoking_options, smoking_default_index = create_numeric_options(0, 40, 1, "0")
                smoking_selected = st.selectbox("Smoking (cigarettes/day)",
                                              options=smoking_options,
                                              index=smoking_default_index,
                                              key="rt_smoking_select")
                smoking = None if smoking_selected == "N/A" else float(smoking_selected)

                # Alcohol with N/A option
                alcohol_options, alcohol_default_index = create_numeric_options(0, 10, 1, "1")
                alcohol_selected = st.selectbox("Alcohol (drinks/day)",
                                              options=alcohol_options,
                                              index=alcohol_default_index,
                                              key="rt_alcohol_select")
                alcohol = None if alcohol_selected == "N/A" else float(alcohol_selected)

                # Social Connection with N/A option
                social_options, social_default_index = create_numeric_options(1, 5, 1, "3")
                social_selected = st.selectbox("Social Connection (1-5 scale)",
                                             options=social_options,
                                             index=social_default_index,
                                             key="rt_social_select")
                social_connection = None if social_selected == "N/A" else float(social_selected)

        # Test Results Tab
        with test_tab:
            col1, col2 = st.columns(2)

            with col1:
                # Test results with Unknown option already included
                malaria = st.selectbox("Malaria", ["Positive", "Negative", "Unknown"], index=2, key="rt_malaria")
                widal_test = st.selectbox("Widal Test", ["Positive", "Negative", "Unknown"], index=2, key="rt_widal")
                hepatitis_b = st.selectbox("Hepatitis B", ["Positive", "Negative", "Unknown"], index=2, key="rt_hep_b")
                voluntary_serology = st.selectbox("Voluntary Serology", ["Positive", "Negative", "Unknown"], index=2, key="rt_serology")

            with col2:
                # Integrated test results
                kidney_function = st.selectbox("Kidney Function", ["Good", "Fair", "Poor", "Unknown"], index=3, key="rt_kidney")
                lipid_profile = st.selectbox("Lipid Profile", ["Normal", "Borderline", "Abnormal", "Unknown"], index=3, key="rt_lipid")
                lung_capacity = st.selectbox("Lung Capacity", ["Normal", "Reduced", "Severely Reduced", "Unknown"], index=3, key="rt_lung")
                liver_function = st.selectbox("Liver Function", ["Normal", "Abnormal", "Unknown"], index=2, key="rt_liver")
                blood_count = st.selectbox("Complete Blood Count", ["Normal", "Abnormal", "Unknown"], index=2, key="rt_cbc")

        submit_button = st.form_submit_button(label="Generate Real-Time Health Score")

        if submit_button:
            # Combine all health data
            health_data = {
                # Vital signs
                "Blood Pressure (Systolic)": "Unknown" if blood_pressure_systolic is None else blood_pressure_systolic,
                "Blood Pressure (Diastolic)": "Unknown" if blood_pressure_diastolic is None else blood_pressure_diastolic,
                "ECG (Heart Rate)": "Unknown" if heart_rate is None else heart_rate,
                "SpO2": "Unknown" if spo2 is None else spo2,
                "Temperature": "Unknown" if temperature is None else temperature,
                "Weight (BMI)": "Unknown" if bmi is None else bmi,
                "Respiratory Rate": "Unknown" if respiratory_rate is None else respiratory_rate,

                # Lifestyle factors
                "Exercise": "Unknown" if exercise is None else exercise,
                "Diet": "Unknown" if diet is None else diet,
                "Sleep": "Unknown" if sleep is None else sleep,
                "Stress": "Unknown" if stress is None else stress,
                "Hydration": "Unknown" if hydration is None else hydration,
                "Smoking": "Unknown" if smoking is None else smoking,
                "Alcohol": "Unknown" if alcohol is None else alcohol,
                "Social Connection": "Unknown" if social_connection is None else social_connection,

                # Test results
                "Glucose": "Unknown" if glucose is None else glucose,
                "Malaria": malaria,
                "Widal Test": widal_test,
                "Hepatitis B": hepatitis_b,
                "Voluntary Serology": voluntary_serology,
                "Kidney Function": kidney_function,  # Keep "Unknown" as is
                "Lipid Profile": lipid_profile,      # Keep "Unknown" as is
                "Lung Capacity": lung_capacity,      # Keep "Unknown" as is
                "Liver Function": liver_function,    # Keep "Unknown" as is
                "Complete Blood Count": blood_count  # Keep "Unknown" as is
            }

            # Submit data for real-time health score analysis
            result = submit_realtime_health_score(health_data)

            if "error" in result:
                response = f"Error generating real-time health score: {result['error']}"
            else:
                # Format the response
                response = "**Real-Time Health Score Analysis**\n\n"
                response += "I've analyzed your comprehensive health data in real-time, combining vital signs, lifestyle factors, and test results. Here's your personalized health assessment:\n\n"

                # Add total score and health status
                total_score = result.get("Total Score", 0)
                health_status = result.get("Health Status", "Unknown")

                # Add emoji based on health status
                if health_status == "Excellent":
                    response += f"**Overall Health Score: {total_score}** - {health_status} 🌟\n\n"
                elif health_status == "Good":
                    response += f"**Overall Health Score: {total_score}** - {health_status} ✅\n\n"
                elif health_status == "Fair":
                    response += f"**Overall Health Score: {total_score}** - {health_status} ⚠️\n\n"
                else:
                    response += f"**Overall Health Score: {total_score}** - {health_status} 🚨\n\n"

                # Add category scores if available
                if "Category Scores" in result:
                    response += "**Category Breakdown:**\n"
                    for category, score in result["Category Scores"].items():
                        # Add emoji based on score
                        if score >= 80:
                            response += f"- {category}: {score} 🌟\n"
                        elif score >= 60:
                            response += f"- {category}: {score} ✅\n"
                        elif score >= 40:
                            response += f"- {category}: {score} ⚠️\n"
                        else:
                            response += f"- {category}: {score} 🚨\n"
                    response += "\n"

                # Add areas needing improvement
                vitals_needing_improvement = result.get("Vitals Needing Improvement", "None")
                if vitals_needing_improvement != "None":
                    response += "**Areas Needing Attention:**\n"
                    for vital in vitals_needing_improvement.split(", "):
                        response += f"- {vital}\n"
                    response += "\n"

                # Add detailed issues if available
                if "Detailed Issues" in result:
                    detailed_issues = result["Detailed Issues"]
                    has_issues = False

                    for category, issues in detailed_issues.items():
                        if issues:
                            if not has_issues:
                                response += "**Detailed Health Insights:**\n"
                                has_issues = True
                            response += f"*{category}*:\n"
                            for issue in issues:
                                response += f"- {issue}\n"

                    if has_issues:
                        response += "\n"

                # Add improvement tips
                improvement_tips = result.get("Improvement Tips", "")
                if improvement_tips:
                    response += "**Personalized Recommendations:**\n"
                    if isinstance(improvement_tips, list):
                        for tip in improvement_tips:
                            response += f"{tip}\n"
                    else:
                        response += f"{improvement_tips}\n"

                # Add a supportive closing note
                response += "\nThis real-time health score provides a comprehensive snapshot of your current health status. Remember that small, consistent improvements can lead to significant long-term benefits."

                # Add device recommendations button with TurboMedics reference
                response += "\n\n**Would you like to see health monitoring devices from TurboMedics based on your results? (Yes/No)**"

                # Add a flag to indicate we're waiting for device recommendation confirmation
                st.session_state.waiting_for_device_recommendation = True
                # Set the current test type for context
                st.session_state.current_recommendation_context = "realtime_health_score"

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my health data for real-time analysis"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_realtime_health_score = False
            st.rerun()

# Test results form (only shown when waiting for test results)
if st.session_state.waiting_for_test_results:
    # Helper function to create options for numeric fields
    def create_numeric_options(start, end, step=1, default=None):
        options = [f"{i:.1f}" if step < 1 else str(i) for i in np.arange(start, end + step, step)]
        options.append("N/A")
        # Set default index
        if default is None or default == "N/A":
            default_index = len(options) - 1  # N/A is the last option
        else:
            # Find the closest value to default
            closest_val = min(options[:-1], key=lambda x: abs(float(x) - float(default)))
            default_index = options.index(closest_val)
        return options, default_index

    # First, show only the test type selection radio buttons
    if st.session_state.test_selection_step:
        with st.form(key="test_type_selection_form"):
            st.markdown("### Malaria and Widal Test Explainer")
            st.markdown("Please select which test you would like to analyze:")

            # Test selection
            test_type = st.radio(
                "Select Test Type",
                ["Malaria Test", "Widal Test"],
                index=0,
                key="test_type_radio"
            )

            submit_test_type = st.form_submit_button(label="Continue")

            if submit_test_type:
                # Store the selected test type and move to the appropriate form
                st.session_state.current_test_type = test_type
                st.session_state.test_selection_step = False

                # Set the appropriate form step based on selection
                if test_type == "Malaria Test":
                    st.session_state.malaria_form_step = True
                    st.session_state.widal_form_step = False
                else:
                    st.session_state.malaria_form_step = False
                    st.session_state.widal_form_step = True

                st.rerun()

    # Show Malaria Test form if selected
    elif st.session_state.malaria_form_step:
        with st.form(key="malaria_test_form"):
            st.markdown("### Malaria Test Explainer")
            st.markdown("Enter your test results for interpretation and recommendations.")

            # Patient information
            st.markdown("#### Patient Information (Optional)")
            col1, col2 = st.columns(2)

            with col1:
                # Age
                age_options, age_default_index = create_numeric_options(1, 120, 1, "40")
                age_selected = st.selectbox(
                    "Age",
                    options=age_options,
                    index=age_default_index,
                    key="malaria_age_select"
                )
                age = None if age_selected == "N/A" else int(age_selected)

            with col2:
                # Sex
                sex = st.selectbox(
                    "Sex",
                    ["Male", "Female", "N/A"],
                    index=0,
                    key="malaria_sex_select"
                )
                sex = None if sex == "N/A" else sex

            # Test result values for Malaria Test
            st.markdown("#### Test Results")

            test_result = st.selectbox(
                "Malaria Test Result",
                ["Positive", "Negative", "Indeterminate"],
                index=1,
                key="malaria_result"
            )

            # Additional malaria test details if positive
            if test_result == "Positive":
                parasite_species = st.selectbox(
                    "Parasite Species (if known)",
                    ["P. falciparum", "P. vivax", "P. ovale", "P. malariae", "P. knowlesi", "Mixed infection", "Unknown"],
                    index=6,
                    key="parasite_species"
                )

                parasite_density = st.text_input(
                    "Parasite Density (if available)",
                    placeholder="e.g., 2%, 10,000/μL, +++ (1-3 plus signs)",
                    key="parasite_density"
                )

                additional_notes = st.text_area(
                    "Additional Test Notes",
                    placeholder="Enter any additional information from your test report",
                    key="malaria_notes"
                )
            else:
                parasite_species = "N/A"
                parasite_density = "N/A"
                additional_notes = ""



            submit_button = st.form_submit_button(label="Analyze Malaria Test")

            if submit_button:
                # Prepare test data for Malaria Test
                test_data = {
                    "selected_tests": ["Malaria Test"],
                    "test_name": "Malaria Test",
                    "patient_age": age,
                    "patient_sex": sex,
                    "malaria": test_result
                }

                # Add additional data if positive
                if test_result == "Positive":
                    test_data["malaria_species"] = parasite_species
                    test_data["parasite_density"] = parasite_density
                    test_data["additional_notes"] = additional_notes

                # Reset form state
                st.session_state.malaria_form_step = False
                st.session_state.test_form_step = True

                # Store the test type for use in response formatting
                test_type = "Malaria Test"

                # Submit test data for analysis
                result = submit_test_results(test_data)

                if "error" in result:
                    response = f"Error analyzing test results: {result['error']}"
                else:
                    # Format the response
                    interpretation = result.get("interpretation", [])
                    recommendations = result.get("recommendations", [])
                    doctor_summary = result.get("doctor_summary", "")

                    # Create a formatted response
                    response = f"**{test_type} Analysis**\n\n"

                    # Add doctor summary
                    if doctor_summary:
                        response += f"{doctor_summary}\n\n"
                    else:
                        # Create a default doctor summary based on test type and result
                        if test_result == "Positive":
                            response += "I've analyzed your malaria test results, which show a positive finding. This indicates the presence of malaria parasites in your blood sample.\n\n"
                        else:
                            response += "I've analyzed your malaria test results, which show a negative finding. This suggests no malaria parasites were detected in your blood sample.\n\n"

                    # Add description
                    response += "**What this test measures:**\nThe malaria test detects the presence of Plasmodium parasites in your blood, which cause malaria. It can identify the specific species of parasite and sometimes the parasite density (level of infection).\n\n"

                    # Add normal ranges
                    response += "**Normal ranges:**\n"
                    response += "- Malaria test: Negative (no parasites detected)\n\n"

                    # Add interpretation
                    response += "**Interpretation:**\n"
                    if interpretation:
                        for item in interpretation:
                            response += f"{item}\n"
                    else:
                        if test_result == "Positive":
                            response += "- Your test shows a positive result for malaria infection\n"
                            if parasite_species != "Unknown" and parasite_species != "N/A":
                                response += f"- The identified parasite species is {parasite_species}\n"
                            if parasite_density != "N/A" and parasite_density:
                                response += f"- Parasite density: {parasite_density}, which indicates the level of infection\n"
                            response += "- Malaria is a serious infection that requires prompt treatment\n\n"
                        else:
                            response += "- Your test shows no evidence of malaria parasites in your blood sample\n"
                            response += "- This suggests you do not currently have an active malaria infection\n\n"

                    # Add recommendations
                    response += "**Recommendations:**\n"
                    if recommendations:
                        for rec in recommendations:
                            response += f"{rec}\n"
                    else:
                        if test_result == "Positive":
                            response += "- Consult with a healthcare provider immediately for appropriate antimalarial treatment\n"
                            response += "- Complete the full course of prescribed medication even if symptoms improve\n"
                            response += "- Rest and drink plenty of fluids to support recovery\n"
                            response += "- Monitor for severe symptoms such as persistent high fever, confusion, or difficulty breathing\n"
                            response += "- Follow up with your healthcare provider after treatment to confirm clearance of the infection\n"
                        else:
                            response += "- If you're experiencing symptoms similar to malaria, consider follow-up testing or alternative diagnoses\n"
                            response += "- Continue to use preventive measures in malaria-endemic areas (bed nets, insect repellent, etc.)\n"
                            response += "- Consider prophylactic medication if traveling to high-risk areas\n"

                    # Add device recommendations button with TurboMedics reference
                    response += "\n\n**Would you like to see health monitoring devices from TurboMedics based on your results? (Yes/No)**"

                    # Add a flag to indicate we're waiting for device recommendation confirmation
                    st.session_state.waiting_for_device_recommendation = True
                    # Set the current test type for context
                    st.session_state.current_recommendation_context = "test_results"

                # Store test results data for follow-up reminders
                st.session_state.test_results_data = test_data
                # Set confirmation type for follow-up tracking
                st.session_state.confirmation_type = "test_results"

                st.session_state.message_log.append({"role": "user", "content": f"I'd like to understand my {test_type} results."})
                st.session_state.message_log.append({"role": "ai", "content": response})
                st.session_state.waiting_for_test_results = False
                st.rerun()

    # Show Widal Test form if selected
    elif st.session_state.widal_form_step:
        with st.form(key="widal_test_form"):
            st.markdown("### Widal Test Explainer")
            st.markdown("Enter your test results for interpretation and recommendations.")

            # Patient information
            st.markdown("#### Patient Information (Optional)")
            col1, col2 = st.columns(2)

            with col1:
                # Age
                age_options, age_default_index = create_numeric_options(1, 120, 1, "40")
                age_selected = st.selectbox(
                    "Age",
                    options=age_options,
                    index=age_default_index,
                    key="widal_age_select"
                )
                age = None if age_selected == "N/A" else int(age_selected)

            with col2:
                # Sex
                sex = st.selectbox(
                    "Sex",
                    ["Male", "Female", "N/A"],
                    index=0,
                    key="widal_sex_select"
                )
                sex = None if sex == "N/A" else sex

            # Test result values for Widal Test
            st.markdown("#### Test Results")
            st.markdown("##### Widal Test Titers")
            col1, col2 = st.columns(2)

            with col1:
                salmonella_typhi_o = st.selectbox(
                    "Typhi O",
                    ["Reactive", "Non-reactive", "N/A"],
                    index=1,
                    key="typhi_o"
                )

                salmonella_typhi_h = st.selectbox(
                    "Typhi H",
                    ["Reactive", "Non-reactive", "N/A"],
                    index=1,
                    key="typhi_h"
                )

            with col2:
                salmonella_paratyphi_a = st.selectbox(
                    "Paratyphi A",
                    ["Reactive", "Non-reactive", "N/A"],
                    index=1,
                    key="paratyphi_a"
                )

                salmonella_paratyphi_b = st.selectbox(
                    "Paratyphi B",
                    ["Reactive", "Non-reactive", "N/A"],
                    index=1,
                    key="paratyphi_b"
                )



            # Set test_result for consistency
            if (salmonella_typhi_o == "Reactive" or
                salmonella_typhi_h == "Reactive" or
                salmonella_paratyphi_a == "Reactive" or
                salmonella_paratyphi_b == "Reactive"):
                test_result = "Positive"
            else:
                test_result = "Negative"



            submit_button = st.form_submit_button(label="Analyze Widal Test")

            if submit_button:
                # Prepare test data for Widal Test
                test_data = {
                    "selected_tests": ["Widal Test (Typhoid)"],
                    "test_name": "Widal Test",
                    "patient_age": age,
                    "patient_sex": sex,
                    "test_result": test_result,
                    "widal": {
                        "Typhi O": salmonella_typhi_o,
                        "Typhi H": salmonella_typhi_h,
                        "Paratyphi A,H": salmonella_paratyphi_a,
                        "Paratyphi B,H": salmonella_paratyphi_b
                    }
                }

                # Reset form state
                st.session_state.widal_form_step = False
                st.session_state.test_form_step = True

                # Store the test type for use in response formatting
                test_type = "Widal Test"

                # Submit test data for analysis
                result = submit_test_results(test_data)

                if "error" in result:
                    response = f"Error analyzing test results: {result['error']}"
                else:
                    # Format the response
                    interpretation = result.get("interpretation", [])
                    recommendations = result.get("recommendations", [])
                    doctor_summary = result.get("doctor_summary", "")

                    # Create a formatted response
                    response = f"**{test_type} Analysis**\n\n"

                    # Add doctor summary
                    if doctor_summary:
                        response += f"{doctor_summary}\n\n"
                    else:
                        # Create a default doctor summary based on test type and result
                        if test_result == "Positive":
                            response += "I've analyzed your Widal test results, which show elevated antibody titers. This may indicate a possible typhoid or paratyphoid infection.\n\n"
                        else:
                            response += "I've analyzed your Widal test results, which show normal antibody titers. This suggests no current typhoid or paratyphoid infection.\n\n"

                    # Add description
                    response += "**What this test measures:**\nThe Widal test measures antibodies against Salmonella typhi and paratyphi in your blood, which cause typhoid and paratyphoid fever. The test reports antibody titers, with higher values potentially indicating infection.\n\n"

                    # Add normal ranges
                    response += "**Normal ranges:**\n"
                    response += "- Widal test: Non-reactive (negative) results are normal\n"
                    response += "- Reactive (positive) results may indicate current or past infection\n\n"

                    # Add interpretation
                    response += "**Interpretation:**\n"
                    if interpretation:
                        for item in interpretation:
                            response += f"{item}\n"
                    else:
                        if test_result == "Positive":
                            response += "- Your Widal test shows reactive results, which may suggest typhoid or paratyphoid infection\n"
                            if salmonella_typhi_o != "N/A":
                                response += f"- Typhi O: {salmonella_typhi_o}\n"
                            if salmonella_typhi_h != "N/A":
                                response += f"- Typhi H: {salmonella_typhi_h}\n"
                            if salmonella_paratyphi_a != "N/A":
                                response += f"- Paratyphi A: {salmonella_paratyphi_a}\n"
                            if salmonella_paratyphi_b != "N/A":
                                response += f"- Paratyphi B: {salmonella_paratyphi_b}\n"
                            response += "- The Widal test should be interpreted alongside clinical symptoms and other tests\n\n"
                        else:
                            response += "- Your Widal test shows non-reactive results\n"
                            response += "- This suggests you likely do not have a current typhoid or paratyphoid infection\n\n"

                    # Add recommendations
                    response += "**Recommendations:**\n"
                    if recommendations:
                        for rec in recommendations:
                            response += f"{rec}\n"
                    else:
                        if test_result == "Positive":
                            response += "- Consult with a healthcare provider for clinical correlation and possible additional testing\n"
                            response += "- Blood culture is the gold standard for diagnosing typhoid fever and may be recommended\n"
                            response += "- If typhoid is confirmed, appropriate antibiotic treatment will be necessary\n"
                            response += "- Maintain good hydration and rest during recovery\n"
                            response += "- Practice strict hand hygiene to prevent spreading the infection to others\n"
                        else:
                            response += "- If you're experiencing symptoms similar to typhoid fever, consider additional testing\n"
                            response += "- Practice good hygiene, including handwashing and food safety\n"
                            response += "- Consider typhoid vaccination if traveling to endemic areas\n"

                    # Add device recommendations button with TurboMedics reference
                    response += "\n\n**Would you like to see health monitoring devices from TurboMedics based on your results? (Yes/No)**"

                    # Add a flag to indicate we're waiting for device recommendation confirmation
                    st.session_state.waiting_for_device_recommendation = True
                    # Set the current test type for context
                    st.session_state.current_recommendation_context = "test_results"

                # Store test results data for follow-up reminders
                st.session_state.test_results_data = test_data
                # Set confirmation type for follow-up tracking
                st.session_state.confirmation_type = "test_results"

                st.session_state.message_log.append({"role": "user", "content": f"I'd like to understand my {test_type} results."})
                st.session_state.message_log.append({"role": "ai", "content": response})
                st.session_state.waiting_for_test_results = False
                st.rerun()







# Lung capacity form (only shown when waiting for lung capacity data)
if st.session_state.waiting_for_lung_capacity:
    with st.form(key="lung_capacity_form"):
        st.markdown("### Enter Your Spirometry Test Results")
        col1, col2 = st.columns(2)

        # Helper function to create options for numeric fields
        def create_numeric_options(start, end, step=1, default=None):
            options = [f"{i:.1f}" if step < 1 else str(i) for i in np.arange(start, end + step, step)]
            options.append("N/A")
            # Set default index
            if default is None or default == "N/A":
                default_index = len(options) - 1  # N/A is the last option
            else:
                # Find the closest value to default
                closest_val = min(options[:-1], key=lambda x: abs(float(x) - float(default)))
                default_index = options.index(closest_val)
            return options, default_index

        with col1:
            # FEV1 (Forced Expiratory Volume in 1 second) with N/A option
            fev1_options, fev1_default_index = create_numeric_options(20.0, 150.0, 0.5, "85.0")
            fev1_selected = st.selectbox("FEV1 (% predicted)",
                                      options=fev1_options,
                                      index=fev1_default_index,
                                      key="lung_fev1_select")
            fev1 = None if fev1_selected == "N/A" else float(fev1_selected)

            # FVC (Forced Vital Capacity) with N/A option
            fvc_options, fvc_default_index = create_numeric_options(20.0, 150.0, 0.5, "90.0")
            fvc_selected = st.selectbox("FVC (% predicted)",
                                     options=fvc_options,
                                     index=fvc_default_index,
                                     key="lung_fvc_select")
            fvc = None if fvc_selected == "N/A" else float(fvc_selected)

            # FEV1/FVC ratio with N/A option
            ratio_options, ratio_default_index = create_numeric_options(0.2, 1.0, 0.01, "0.75")
            ratio_selected = st.selectbox("FEV1/FVC ratio",
                                       options=ratio_options,
                                       index=ratio_default_index,
                                       key="lung_ratio_select")
            fev1_fvc_ratio = None if ratio_selected == "N/A" else float(ratio_selected)

            # PEF (Peak Expiratory Flow) with N/A option
            pef_options, pef_default_index = create_numeric_options(20.0, 150.0, 0.5, "85.0")
            pef_selected = st.selectbox("PEF (% predicted)",
                                     options=pef_options,
                                     index=pef_default_index,
                                     key="lung_pef_select")
            pef = None if pef_selected == "N/A" else float(pef_selected)

        with col2:
            # FEF25-75 (Forced Expiratory Flow at 25-75%) with N/A option
            fef_options, fef_default_index = create_numeric_options(20.0, 150.0, 0.5, "75.0")
            fef_selected = st.selectbox("FEF25-75 (% predicted)",
                                     options=fef_options,
                                     index=fef_default_index,
                                     key="lung_fef_select")
            fef25_75 = None if fef_selected == "N/A" else float(fef_selected)

            # Height with N/A option
            height_options, height_default_index = create_numeric_options(120.0, 220.0, 1.0, "170.0")
            height_selected = st.selectbox("Height (cm)",
                                        options=height_options,
                                        index=height_default_index,
                                        key="lung_height_select")
            height = None if height_selected == "N/A" else float(height_selected)

            # Age with N/A option
            age_options, age_default_index = create_numeric_options(1, 120, 1, "40")
            age_selected = st.selectbox("Age",
                                     options=age_options,
                                     index=age_default_index,
                                     key="lung_age_select")
            age = None if age_selected == "N/A" else int(age_selected)

            # Sex selection (no N/A option needed as it's already a dropdown)
            sex = st.selectbox("Sex", ["Male", "Female", "N/A"], index=0, key="lung_sex_select")

            # Race selection for peak flow calculation
            race = st.selectbox("Race/Ethnicity",
                             ["Caucasian", "African American", "Asian", "Hispanic/Latino", "Middle Eastern", "N/A"],
                             index=0,
                             key="lung_race_select")

            # Smoking status
            smoking_status = st.selectbox("Smoking Status",
                                       ["Non-smoker", "Former smoker", "Current smoker", "N/A"],
                                       index=0,
                                       key="lung_smoking_select")

        submit_button = st.form_submit_button(label="Analyze Lung Capacity")

        if submit_button:
            # Only include values that are not None or N/A
            spirometry_data = {}

            if fev1 is not None:
                spirometry_data["FEV1"] = fev1
            if fvc is not None:
                spirometry_data["FVC"] = fvc
            if fev1_fvc_ratio is not None:
                spirometry_data["FEV1_FVC_ratio"] = fev1_fvc_ratio
            if pef is not None:
                spirometry_data["PEF"] = pef
            if fef25_75 is not None:
                spirometry_data["FEF25_75"] = fef25_75
            if age is not None:
                spirometry_data["Age"] = age
            if height is not None:
                spirometry_data["Height"] = height

            # Handle dropdown selections
            if sex != "N/A":
                spirometry_data["Sex"] = sex
            if race != "N/A":
                spirometry_data["Race"] = race
            if smoking_status != "N/A":
                spirometry_data["Smoking_Status"] = smoking_status

            result = submit_lung_capacity(spirometry_data)

            if "error" in result:
                response = f"Error analyzing lung capacity: {result['error']}"
            else:
                # Format the response
                analysis_items = result.get("analysis", [])
                risk_level = result.get("respiratory_risk_level", "Unknown")
                conditions = result.get("potential_conditions", [])
                recommendations = result.get("recommendations", [])
                formatted_recs = result.get("formatted_recommendations", [])
                confidence_level = result.get("confidence_level", "Unknown")
                missing_parameters = result.get("missing_parameters", [])

                # Create a formatted response in a friendly doctor's tone
                response = "**Lung Capacity Analysis**\n\n"
                response += "Hi there! I've reviewed your spirometry results, and here's what I'm seeing:\n\n"

                # Add a summary based on risk level
                if risk_level.lower() == "low":
                    response += "👍 **Good news!** Your lung function appears to be in good shape overall. "
                elif risk_level.lower() == "moderate":
                    response += "⚠️ **I'm noticing some areas of concern** in your lung function that we should keep an eye on. "
                elif risk_level.lower() == "high":
                    response += "⚠️ **I'm seeing some significant concerns** with your lung function that we should address. "
                else:
                    response += "I've analyzed your lung function test results. "

                # Add a note about confidence
                if confidence_level.lower() == "high":
                    response += "The test provided comprehensive data, so I'm quite confident in this assessment.\n\n"
                elif confidence_level.lower() == "moderate":
                    response += "The test provided most of the key measurements, giving us a reasonably good picture of your lung health.\n\n"
                elif confidence_level.lower() == "low":
                    response += f"We're missing some measurements ({', '.join(missing_parameters)}), so this is a preliminary assessment.\n\n"

                # Add analysis results in a conversational way
                if analysis_items:
                    response += "**Here's what your results tell me:**\n"
                    for item in analysis_items:
                        response += f"- {item}\n"
                    response += "\n"

                # Add potential conditions in a careful, supportive way
                if conditions and conditions != ["No specific respiratory conditions identified"]:
                    response += "**Based on these patterns, I'm seeing indicators that could be associated with:**\n"
                    for condition in conditions:
                        if "COPD" in condition:
                            response += f"- {condition} - This is a progressive lung disease that makes breathing difficult\n"
                        elif "asthma" in condition.lower():
                            response += f"- {condition} - This causes airways to narrow and swell, making breathing difficult\n"
                        elif "restrictive" in condition.lower():
                            response += f"- {condition} - This means the lungs can't fully expand\n"
                        else:
                            response += f"- {condition}\n"
                    response += "\n"
                    response += "Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis. A full evaluation by a pulmonologist would be needed to confirm.\n\n"

                # Add recommendations in a supportive, actionable way
                if formatted_recs or recommendations:
                    response += "**Here's what I recommend:**\n"
                    if formatted_recs:
                        for rec in formatted_recs:
                            response += f"{rec}\n"
                    elif recommendations:
                        for rec in recommendations:
                            response += f"- {rec}\n"
                    response += "\n"

                # Add a supportive closing note
                if risk_level.lower() == "low":
                    response += "Keep up the good work with your lung health! Regular exercise and avoiding respiratory irritants will help maintain your healthy lung function."
                elif risk_level.lower() in ["moderate", "high"]:
                    response += "I'm here to help you improve your lung health. Let me know if you have any questions about these results or recommendations."
                else:
                    response += "I'm here to help you understand and improve your lung health. Feel free to ask any questions about these results."

                # Add device recommendations button
                response += "\n\n**Would you like to see recommended health devices based on your results? (Yes/No)**"

                # Add a flag to indicate we're waiting for device recommendation confirmation
                st.session_state.waiting_for_device_recommendation = True
                # Set the current test type for context
                st.session_state.current_recommendation_context = "lung_capacity"

            # Store lung capacity data for follow-up reminders
            st.session_state.lung_capacity_data = spirometry_data
            # Set confirmation type for follow-up tracking
            st.session_state.confirmation_type = "lung_capacity"

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my spirometry test results for analysis: {json.dumps(spirometry_data, indent=2)}"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_lung_capacity = False
            st.rerun()

# Vital signs form (only shown when waiting for vitals)
if st.session_state.waiting_for_vitals:
    # Get default health data from the server
    default_data = get_default_health_data()

    with st.form(key="vital_signs_form"):
        st.markdown("### Enter Your Vital Signs")
        col1, col2 = st.columns(2)

        # Add a note about N/A values
        # st.info("For any field you don't have data for, select 'N/A' from the dropdown.")

        # Helper function to create options for numeric fields
        def create_numeric_options(start, end, step=1, default=None):
            options = [f"{i:.1f}" if step < 1 else str(i) for i in np.arange(start, end + step, step)]
            options.append("N/A")
            # Set default index
            if default is None or default == "N/A":
                default_index = len(options) - 1  # N/A is the last option
            else:
                # Find the closest value to default
                closest_val = min(options[:-1], key=lambda x: abs(float(x) - float(default)))
                default_index = options.index(closest_val)
            return options, default_index

        with col1:
            # Blood Pressure (Systolic) with N/A option
            bp_sys_default = "120" if default_data.get("Blood Pressure (Systolic)") is None else str(default_data.get("Blood Pressure (Systolic)"))
            bp_sys_options, bp_sys_default_index = create_numeric_options(70, 200, 1, bp_sys_default)
            bp_sys_selected = st.selectbox("Blood Pressure (Systolic)",
                                         options=bp_sys_options,
                                         index=bp_sys_default_index,
                                         key="vital_bp_sys_select")
            blood_pressure_systolic = None if bp_sys_selected == "N/A" else float(bp_sys_selected)

            # Blood Pressure (Diastolic) with N/A option
            bp_dia_default = "80" if default_data.get("Blood Pressure (Diastolic)") is None else str(default_data.get("Blood Pressure (Diastolic)"))
            bp_dia_options, bp_dia_default_index = create_numeric_options(40, 120, 1, bp_dia_default)
            bp_dia_selected = st.selectbox("Blood Pressure (Diastolic)",
                                         options=bp_dia_options,
                                         index=bp_dia_default_index,
                                         key="vital_bp_dia_select")
            blood_pressure_diastolic = None if bp_dia_selected == "N/A" else float(bp_dia_selected)

            # Heart Rate with N/A option
            hr_default = "75" if default_data.get("ECG (Heart Rate)") is None else str(default_data.get("ECG (Heart Rate)"))
            hr_options, hr_default_index = create_numeric_options(40, 200, 1, hr_default)
            hr_selected = st.selectbox("Heart Rate (bpm)",
                                     options=hr_options,
                                     index=hr_default_index,
                                     key="vital_hr_select")
            heart_rate = None if hr_selected == "N/A" else float(hr_selected)

        with col2:
            # Temperature with N/A option
            temp_default = "36.8" if default_data.get("Temperature") is None else str(default_data.get("Temperature"))
            temp_options, temp_default_index = create_numeric_options(35.0, 42.0, 0.1, temp_default)
            temp_selected = st.selectbox("Temperature (°C)",
                                       options=temp_options,
                                       index=temp_default_index,
                                       key="vital_temp_select")
            temperature = None if temp_selected == "N/A" else float(temp_selected)

            # Glucose with N/A option
            glucose_default = "100" if default_data.get("Glucose") is None else str(default_data.get("Glucose"))
            glucose_options, glucose_default_index = create_numeric_options(50, 300, 1, glucose_default)
            glucose_selected = st.selectbox("Glucose (mg/dL)",
                                          options=glucose_options,
                                          index=glucose_default_index,
                                          key="vital_glucose_select")
            glucose = None if glucose_selected == "N/A" else float(glucose_selected)

            # SpO2 with N/A option
            spo2_default = "98" if default_data.get("SpO2") is None else str(default_data.get("SpO2"))
            spo2_options, spo2_default_index = create_numeric_options(80, 100, 1, spo2_default)
            spo2_selected = st.selectbox("SpO2 (%)",
                                       options=spo2_options,
                                       index=spo2_default_index,
                                       key="vital_spo2_select")
            spo2 = None if spo2_selected == "N/A" else float(spo2_selected)

        submit_button = st.form_submit_button(label="Submit Vital Signs")

        if submit_button:
            # Only include values that are not None (N/A selections)
            vital_signs = {}
            if blood_pressure_systolic is not None:
                vital_signs["Blood_Pressure_Systolic"] = blood_pressure_systolic
            if blood_pressure_diastolic is not None:
                vital_signs["Blood_Pressure_Diastolic"] = blood_pressure_diastolic
            if heart_rate is not None:
                vital_signs["Heart_Rate"] = heart_rate
            if temperature is not None:
                vital_signs["Temperature"] = temperature
            if glucose is not None:
                vital_signs["Glucose"] = glucose
            if spo2 is not None:
                vital_signs["SpO2"] = spo2

            result = submit_vital_signs(vital_signs)

            if "error" in result:
                response = f"Error processing vital signs: {result['error']}"
            else:
                # Data is automatically saved on the server side in the submit_vital_signs function

                response = f"**Vital Signs Analysis**\n\n"
                response += "Hello! I've reviewed your vital signs, and here's my assessment:\n\n"

                # Get the analysis and alerts
                analysis = result.get('analysis', '')
                alerts = result.get('alerts', '')

                # Check if there are alerts to determine overall tone
                if alerts:
                    response += "I'm noticing some values that need attention. Let me walk you through what I'm seeing:\n\n"
                else:
                    response += "Good news! Your vital signs are looking stable. Here's a breakdown:\n\n"

                # Add the analysis in a conversational way
                response += f"{analysis}\n"

                # Add alerts with a caring tone if present
                if alerts:
                    response += f"\n**Important to Note**:\n{alerts}\n\n"
                    response += "These findings suggest we should keep an eye on these values. Regular monitoring can help us track any changes and address them promptly."
                else:
                    response += "\nKeep up the good work! Regular monitoring of your vital signs is an excellent way to stay on top of your health."

                # Add device recommendations button with TurboMedics reference
                response += "\n\n**Would you like to see health monitoring devices from TurboMedics based on your results? (Yes/No)**"

                # Add a flag to indicate we're waiting for device recommendation confirmation
                st.session_state.waiting_for_device_recommendation = True
                # Set the current test type for context
                st.session_state.current_recommendation_context = "vital_signs"

                # Add note about saved data
                # response += "\n\n*Your vital signs data has been saved on the server. You can ask for personalized recommendations anytime.*"

            # Store vital signs data for follow-up reminders
            st.session_state.vital_signs_data = vital_signs
            # Set confirmation type for follow-up tracking
            st.session_state.confirmation_type = "vital_signs"

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my vital signs: {json.dumps(vital_signs, indent=2)}"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_vitals = False
            st.rerun()



# Kidney function test form (only shown when waiting for kidney function test)
if st.session_state.waiting_for_kidney_function:
    with st.form(key="kidney_function_form"):
        st.markdown("### Enter Your Kidney Function Test Results")
        col1, col2 = st.columns(2)

        # Helper function to create options for numeric fields
        def create_numeric_options(start, end, step=1, default=None):
            options = [f"{i:.1f}" if step < 1 else str(i) for i in np.arange(start, end + step, step)]
            options.append("N/A")
            # Set default index
            if default is None or default == "N/A":
                default_index = len(options) - 1  # N/A is the last option
            else:
                # Find the closest value to default
                closest_val = min(options[:-1], key=lambda x: abs(float(x) - float(default)))
                default_index = options.index(closest_val)
            return options, default_index

        with col1:
            # Serum Urea with N/A option
            urea_options, urea_default_index = create_numeric_options(0.0, 200.0, 0.1, "5.0")
            urea_selected = st.selectbox("Serum Urea",
                                       options=urea_options,
                                       index=urea_default_index,
                                       key="kidney_urea_select")
            serum_urea = None if urea_selected == "N/A" else float(urea_selected)

            # Serum Creatinine with N/A option
            creatinine_options, creatinine_default_index = create_numeric_options(0.0, 20.0, 0.1, "1.0")
            creatinine_selected = st.selectbox("Serum Creatinine",
                                             options=creatinine_options,
                                             index=creatinine_default_index,
                                             key="kidney_creatinine_select")
            serum_creatinine = None if creatinine_selected == "N/A" else float(creatinine_selected)

            # Serum Sodium with N/A option
            sodium_options, sodium_default_index = create_numeric_options(100.0, 200.0, 0.1, "140.0")
            sodium_selected = st.selectbox("Serum Sodium",
                                         options=sodium_options,
                                         index=sodium_default_index,
                                         key="kidney_sodium_select")
            serum_sodium = None if sodium_selected == "N/A" else float(sodium_selected)

            # Serum Potassium with N/A option
            potassium_options, potassium_default_index = create_numeric_options(1.0, 10.0, 0.1, "4.0")
            potassium_selected = st.selectbox("Serum Potassium",
                                            options=potassium_options,
                                            index=potassium_default_index,
                                            key="kidney_potassium_select")
            serum_potassium = None if potassium_selected == "N/A" else float(potassium_selected)

            # Serum Calcium with N/A option
            calcium_options, calcium_default_index = create_numeric_options(5.0, 15.0, 0.1, "9.5")
            calcium_selected = st.selectbox("Serum Calcium",
                                          options=calcium_options,
                                          index=calcium_default_index,
                                          key="kidney_calcium_select")
            serum_calcium = None if calcium_selected == "N/A" else float(calcium_selected)

            # Serum Uric Acid with N/A option
            uric_acid_options, uric_acid_default_index = create_numeric_options(1.0, 20.0, 0.1, "5.0")
            uric_acid_selected = st.selectbox("Serum Uric Acid",
                                            options=uric_acid_options,
                                            index=uric_acid_default_index,
                                            key="kidney_uric_acid_select")
            serum_uric_acid = None if uric_acid_selected == "N/A" else float(uric_acid_selected)

        with col2:
            # Urine Albumin with N/A option
            albumin_options, albumin_default_index = create_numeric_options(0.0, 1000.0, 1.0, "10.0")
            albumin_selected = st.selectbox("Urine Albumin",
                                          options=albumin_options,
                                          index=albumin_default_index,
                                          key="kidney_albumin_select")
            urine_albumin = None if albumin_selected == "N/A" else float(albumin_selected)

            # Urine Creatinine with N/A option
            urine_creat_options, urine_creat_default_index = create_numeric_options(0.0, 500.0, 1.0, "100.0")
            urine_creat_selected = st.selectbox("Urine Creatinine",
                                              options=urine_creat_options,
                                              index=urine_creat_default_index,
                                              key="kidney_urine_creat_select")
            urine_creatinine = None if urine_creat_selected == "N/A" else float(urine_creat_selected)

            # Chloride with N/A option
            chloride_options, chloride_default_index = create_numeric_options(50.0, 150.0, 0.1, "100.0")
            chloride_selected = st.selectbox("Chloride",
                                           options=chloride_options,
                                           index=chloride_default_index,
                                           key="kidney_chloride_select")
            chloride = None if chloride_selected == "N/A" else float(chloride_selected)

            # Bicarbonate with N/A option
            bicarb_options, bicarb_default_index = create_numeric_options(10.0, 50.0, 0.1, "25.0")
            bicarb_selected = st.selectbox("Bicarbonate",
                                         options=bicarb_options,
                                         index=bicarb_default_index,
                                         key="kidney_bicarb_select")
            bicarbonate = None if bicarb_selected == "N/A" else float(bicarb_selected)

            # Age with N/A option
            age_options, age_default_index = create_numeric_options(1, 120, 1, "40")
            age_selected = st.selectbox("Age",
                                      options=age_options,
                                      index=age_default_index,
                                      key="kidney_age_select")
            age = None if age_selected == "N/A" else int(age_selected)

            # Sex selection (no N/A option needed as it's already a dropdown)
            sex = st.selectbox("Sex", ["Male", "Female", "N/A"], index=0, key="kidney_sex_select")

        submit_button = st.form_submit_button(label="Analyze Kidney Function")

        if submit_button:
            # Only include values that are not None (N/A selections)
            kidney_data = {}
            if serum_urea is not None:
                kidney_data["Serum Urea"] = serum_urea
            if serum_creatinine is not None:
                kidney_data["Serum Creatinine"] = serum_creatinine
            if serum_sodium is not None:
                kidney_data["Serum Sodium"] = serum_sodium
            if serum_potassium is not None:
                kidney_data["Serum Potassium"] = serum_potassium
            if serum_calcium is not None:
                kidney_data["Serum Calcium"] = serum_calcium
            if serum_uric_acid is not None:
                kidney_data["Serum Uric Acid"] = serum_uric_acid
            if urine_albumin is not None:
                kidney_data["Urine Albumin"] = urine_albumin
            if urine_creatinine is not None:
                kidney_data["Urine Creatinine"] = urine_creatinine
            if chloride is not None:
                kidney_data["Chloride"] = chloride
            if bicarbonate is not None:
                kidney_data["Bicarbonate"] = bicarbonate
            if age is not None:
                kidney_data["Age"] = age
            if sex != "N/A":
                kidney_data["Sex"] = sex

            result = submit_kidney_function(kidney_data)

            if "error" in result:
                response = f"Error analyzing kidney function: {result['error']}"
            else:
                # Data is automatically saved on the server side in the submit_kidney_function function

                # Format the response
                analysis_items = result.get("analysis", "")
                formatted_analysis = ""
                if isinstance(analysis_items, list):
                    formatted_analysis = "**Analysis:**\n"
                    for item in analysis_items:
                        formatted_analysis += f"- {item}\n"
                elif isinstance(analysis_items, str):
                    # If it's a string, try to split by newlines and format as list
                    lines = analysis_items.split("\n")
                    formatted_analysis = "**Analysis Results:**\n"
                    for line in lines:
                        if line.strip():  # Skip empty lines
                            formatted_analysis += f"- {line}\n"

                overall_health = result.get("overall_health", "Unknown")
                confidence_level = result.get("confidence_level", "Unknown")
                missing_parameters = result.get("missing_parameters", [])
                recommendations = result.get("recommendations", [])

                response = f"**Kidney Function Analysis**\n\n"
                response += "Hello! I've reviewed your kidney function test results, and here's my assessment:\n\n"

                # Add a summary based on overall health
                if "normal" in overall_health.lower() or "healthy" in overall_health.lower():
                    response += "👍 **Good news!** Your kidney function appears to be in the healthy range. "
                elif "mild" in overall_health.lower():
                    response += "⚠️ **I'm seeing some mild changes** in your kidney function that we should monitor. "
                elif "moderate" in overall_health.lower() or "concerning" in overall_health.lower():
                    response += "⚠️ **I'm noticing some concerning patterns** in your kidney function that we should address. "
                elif "severe" in overall_health.lower() or "significant" in overall_health.lower():
                    response += "⚠️ **I'm seeing significant concerns** with your kidney function that require attention. "
                else:
                    response += "I've analyzed your kidney function test results. "

                # Add a note about confidence
                if confidence_level.lower() == "high":
                    response += "Your test provided comprehensive data, so I'm quite confident in this assessment.\n\n"
                elif confidence_level.lower() == "moderate":
                    response += "Your test provided most of the key measurements, giving us a reasonably good picture of your kidney health.\n\n"
                elif confidence_level.lower() == "low":
                    response += f"We're missing some measurements ({', '.join(missing_parameters)}), so this is a preliminary assessment.\n\n"

                # Add detailed findings in a conversational way
                response += f"**Here's what I'm seeing in your results:**\n{overall_health}\n\n"

                # Add analysis results in a conversational way
                if formatted_analysis:
                    response += "**The details behind my assessment:**\n"
                    response += f"{formatted_analysis}\n"

                # Add recommendations in a supportive, actionable way
                if recommendations:
                    response += "**Here's what I recommend:**\n"
                    for rec in recommendations:
                        if not rec.startswith("-"):
                            response += f"- {rec}\n"
                        else:
                            response += f"{rec}\n"
                    response += "\n"

                # Add a supportive closing note
                if "normal" in overall_health.lower() or "healthy" in overall_health.lower():
                    response += "Keep up the good work! Staying hydrated and maintaining a balanced diet will help preserve your kidney health."
                else:
                    response += "I'm here to help you understand and improve your kidney health. Let me know if you have any questions about these results or recommendations."

                # Add device recommendations button
                response += "\n\n**Would you like to see recommended health devices based on your results? (Yes/No)**"

                # Add a flag to indicate we're waiting for device recommendation confirmation
                st.session_state.waiting_for_device_recommendation = True
                # Set the current test type for context
                st.session_state.current_recommendation_context = "kidney_function"

                # response += "You can ask me for more specific recommendations based on your test results at any time."

                # Add note about saved data
                # response += "\n\n*Your kidney function data has been saved on the server. You can ask for personalized recommendations anytime.*"

            # Store kidney function data for follow-up reminders
            st.session_state.kidney_function_data = kidney_data
            # Set confirmation type for follow-up tracking
            st.session_state.confirmation_type = "kidney_function"

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my kidney function test results for analysis: {json.dumps(kidney_data, indent=2)}"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_kidney_function = False
            st.rerun()

# Lipid profile test form (only shown when waiting for lipid profile test)
if st.session_state.waiting_for_lipid_profile:
    with st.form(key="lipid_profile_form"):
        st.markdown("### Enter Your Lipid Profile for Cardiac Health Planning")
        col1, col2 = st.columns(2)

        # Add a note about N/A values
        # st.info("For any field you don't have data for, select 'N/A' from the dropdown.")

        # Helper function to create options for numeric fields
        def create_numeric_options(start, end, step=1, default=None):
            options = [f"{i:.1f}" if step < 1 else str(i) for i in np.arange(start, end + step, step)]
            options.append("N/A")
            # Set default index
            if default is None or default == "N/A":
                default_index = len(options) - 1  # N/A is the last option
            else:
                # Find the closest value to default
                closest_val = min(options[:-1], key=lambda x: abs(float(x) - float(default)))
                default_index = options.index(closest_val)
            return options, default_index

        with col1:
            # Total Cholesterol with N/A option
            total_chol_options, total_chol_default_index = create_numeric_options(100, 400, 1, "200")
            total_chol_selected = st.selectbox("Total Cholesterol (mg/dL)",
                                             options=total_chol_options,
                                             index=total_chol_default_index,
                                             key="lipid_total_chol_select")
            total_chol = None if total_chol_selected == "N/A" else float(total_chol_selected)

            # LDL Cholesterol with N/A option
            ldl_options, ldl_default_index = create_numeric_options(30, 300, 1, "130")
            ldl_selected = st.selectbox("LDL Cholesterol (mg/dL)",
                                      options=ldl_options,
                                      index=ldl_default_index,
                                      key="lipid_ldl_select")
            ldl = None if ldl_selected == "N/A" else float(ldl_selected)

            # HDL Cholesterol with N/A option
            hdl_options, hdl_default_index = create_numeric_options(20, 100, 1, "50")
            hdl_selected = st.selectbox("HDL Cholesterol (mg/dL)",
                                      options=hdl_options,
                                      index=hdl_default_index,
                                      key="lipid_hdl_select")
            hdl = None if hdl_selected == "N/A" else float(hdl_selected)

            # Triglycerides with N/A option
            trig_options, trig_default_index = create_numeric_options(50, 1000, 1, "150")
            trig_selected = st.selectbox("Triglycerides (mg/dL)",
                                       options=trig_options,
                                       index=trig_default_index,
                                       key="lipid_trig_select")
            triglycerides = None if trig_selected == "N/A" else float(trig_selected)

            # Non-HDL Cholesterol with N/A option
            non_hdl_options, non_hdl_default_index = create_numeric_options(50, 200, 1, "110")
            non_hdl_selected = st.selectbox("Non-HDL Cholesterol (mg/dL)",
                                          options=non_hdl_options,
                                          index=non_hdl_default_index,
                                          key="lipid_non_hdl_select")
            non_hdl = None if non_hdl_selected == "N/A" else float(non_hdl_selected)

            # VLDL Cholesterol with N/A option
            vldl_options, vldl_default_index = create_numeric_options(20, 50, 1, "45")
            vldl_selected = st.selectbox("VLDL Cholesterol (mg/dL)",
                                       options=vldl_options,
                                       index=vldl_default_index,
                                       key="lipid_vldl_select")
            vldl = None if vldl_selected == "N/A" else float(vldl_selected)

        with col2:
            # Age with N/A option
            age_options, age_default_index = create_numeric_options(18, 120, 1, "40")
            age_selected = st.selectbox("Age",
                                      options=age_options,
                                      index=age_default_index,
                                      key="lipid_age_select")
            age = None if age_selected == "N/A" else int(age_selected)

            # Sex selection (no N/A option needed as it's already a dropdown)
            sex = st.selectbox("Sex", ["Male", "Female", "N/A"], index=0, key="lipid_sex")

            # Other risk factors with N/A option
            smoker = st.selectbox("Smoker", ["Yes", "No", "N/A"], index=1, key="lipid_smoker")
            hypertension = st.selectbox("Hypertension", ["Yes", "No", "N/A"], index=1, key="lipid_hypertension")
            diabetes = st.selectbox("Diabetes", ["Yes", "No", "N/A"], index=1, key="lipid_diabetes")
            family_history = st.selectbox("Family History of Heart Disease", ["Yes", "No", "N/A"], index=1, key="lipid_family_history")

        submit_button = st.form_submit_button(label="Analyze Cardiac Health")

        if submit_button:
            # Only include values that are not None or N/A
            lipid_data = {}

            if total_chol is not None:
                lipid_data["total_chol"] = total_chol
            if ldl is not None:
                lipid_data["ldl"] = ldl
            if hdl is not None:
                lipid_data["hdl"] = hdl
            if triglycerides is not None:
                lipid_data["triglycerides"] = triglycerides
            if non_hdl is not None:
                lipid_data["non_hdl"] = non_hdl
            if vldl is not None:
                lipid_data["vldl"] = vldl
            if age is not None:
                lipid_data["age"] = age

            # Handle dropdown selections
            if sex != "N/A":
                lipid_data["sex"] = sex
            if smoker != "N/A":
                lipid_data["smoker"] = smoker
            if hypertension != "N/A":
                lipid_data["hypertension"] = hypertension
            if diabetes != "N/A":
                lipid_data["diabetes"] = diabetes
            if family_history != "N/A":
                lipid_data["family_history"] = family_history

            result = submit_lipid_profile(lipid_data)

            if "error" in result:
                response = f"Error analyzing lipid profile: {result['error']}"
            else:
                # Data is automatically saved on the server side in the submit_lipid_profile function

                # Format the response
                classification = result.get("classification", {})
                risk = result.get("ascvd_risk", "Unknown")
                recommendations = result.get("recommendations", [])
                formatted_recs = result.get("formatted_recommendations", [])
                ref_ranges = result.get("ref_ranges", {})
                parameter_explanations = result.get("parameter_explanations", [])
                diet_plan = result.get("diet_plan", {})
                cardiac_health_plan = result.get("cardiac_health_plan", {})

                # Create a formatted response in a friendly doctor's tone
                response = "**Lipid Profile Analysis**\n\n"

                # Add doctor's summary if available
                if "doctor_summary" in result and result["doctor_summary"]:
                    response += f"{result['doctor_summary']}\n\n"
                else:
                    # Add a summary based on risk level
                    if "low" in risk.lower():
                        response += "👍 **Good news!** Your cardiovascular risk based on these lipid values appears to be low. "
                    elif "moderate" in risk.lower() or "intermediate" in risk.lower():
                        response += "⚠️ **I'm seeing a moderate level of cardiovascular risk** based on your lipid profile. "
                    elif "high" in risk.lower() or "elevated" in risk.lower():
                        response += "⚠️ **I'm noticing an elevated cardiovascular risk** based on your lipid profile that we should address. "
                    else:
                        response += "I've analyzed your lipid profile results. "

                    # Add a note about the importance of lipid profile
                    response += "Your lipid profile is an important indicator of heart health and can help us prevent cardiovascular issues.\n\n"

                # Add classification results in a conversational way
                response += "**Here's a breakdown of your results:**\n"
                for component, level in classification.items():
                    component_name = component.replace('_', ' ').title()

                    # Add emoji indicators
                    emoji = "✅" if level.lower() == "optimal" or level.lower() == "normal" else "⚠️" if level.lower() == "borderline" else "❗" if level.lower() == "high" or level.lower() == "low" else "📊"

                    response += f"{emoji} **{component_name}**: {level.title()} "

                    # Add reference range if available
                    if component in ref_ranges:
                        ranges = ref_ranges[component]
                        if level in ranges:
                            response += f"({ranges[level]})"
                    response += "\n"

                # Add risk assessment with more context
                response += f"\n**Your Cardiovascular Risk Assessment**: {risk}\n"

                if "low" in risk.lower():
                    response += "This means your risk of developing cardiovascular disease in the next 10 years is relatively low based on your lipid values.\n"
                elif "moderate" in risk.lower() or "intermediate" in risk.lower() or "borderline" in risk.lower():
                    response += "This suggests you have some risk factors that could increase your chances of developing cardiovascular disease in the next 10 years.\n"
                elif "high" in risk.lower() or "elevated" in risk.lower():
                    response += "This indicates a higher probability of developing cardiovascular disease in the next 10 years, and we should take proactive steps to address this.\n"

                # Add detailed parameter explanations
                if parameter_explanations:
                    response += "\n**Understanding Your Lipid Profile:**\n"
                    for param in parameter_explanations:
                        response += f"- **{param['parameter']}** ({param['value']} mg/dL): {param['explanation']} {param['status_explanation']}\n"

                # Add recommendations in a supportive, actionable way
                if formatted_recs or recommendations:
                    response += "\n**Here's what I recommend:**\n"
                    if formatted_recs:
                        for rec in formatted_recs:
                            response += f"{rec}\n"
                    elif recommendations:
                        for rec in recommendations:
                            response += f"- {rec}\n"

                # Add diet plan
                if diet_plan:
                    response += f"\n\n**{diet_plan.get('title', 'Heart-Healthy Diet Plan')}**\n"
                    response += f"{diet_plan.get('overview', '')}\n\n"

                    response += "**Recommended Foods:**\n"
                    for food in diet_plan.get('recommended_foods', [])[:5]:  # Limit to 5 items
                        response += f"- {food}\n"

                    response += "\n**Foods to Limit:**\n"
                    for food in diet_plan.get('foods_to_limit', [])[:5]:  # Limit to 5 items
                        response += f"- {food}\n"

                    response += "\n**Sample Meal Ideas:**\n"

                    meal_suggestions = diet_plan.get('meal_suggestions', {})
                    if 'breakfast' in meal_suggestions and meal_suggestions['breakfast']:
                        response += "\n*Breakfast Options:*\n"
                        for meal in meal_suggestions['breakfast'][:2]:  # Limit to 2 examples
                            response += f"- {meal}\n"

                    if 'lunch' in meal_suggestions and meal_suggestions['lunch']:
                        response += "\n*Lunch Options:*\n"
                        for meal in meal_suggestions['lunch'][:2]:  # Limit to 2 examples
                            response += f"- {meal}\n"

                    if 'dinner' in meal_suggestions and meal_suggestions['dinner']:
                        response += "\n*Dinner Options:*\n"
                        for meal in meal_suggestions['dinner'][:2]:  # Limit to 2 examples
                            response += f"- {meal}\n"

                # Add cardiac health plan
                if cardiac_health_plan:
                    response += f"\n\n**{cardiac_health_plan.get('title', 'Cardiac Health Plan')}**\n"
                    response += f"{cardiac_health_plan.get('overview', '')}\n\n"

                    if cardiac_health_plan.get('monitoring_recommendations'):
                        response += "**Monitoring Recommendations:**\n"
                        for rec in cardiac_health_plan.get('monitoring_recommendations', [])[:3]:  # Limit to 3 items
                            response += f"- {rec}\n"

                    if cardiac_health_plan.get('lifestyle_changes'):
                        response += "\n**Lifestyle Changes:**\n"
                        for change in cardiac_health_plan.get('lifestyle_changes', [])[:3]:  # Limit to 3 items
                            response += f"- {change}\n"

                    if cardiac_health_plan.get('follow_up'):
                        response += f"\n**Follow-up Plan:**\n{cardiac_health_plan.get('follow_up')}\n"

                    if cardiac_health_plan.get('potential_treatments'):
                        response += "\n**Potential Treatments to Discuss with Your Doctor:**\n"
                        for treatment in cardiac_health_plan.get('potential_treatments', [])[:3]:  # Limit to 3 items
                            response += f"- {treatment}\n"

                # Add a supportive closing note
                response += "\n\nI'm here to help you improve your cardiovascular health. Small, consistent changes to diet and lifestyle can make a big difference. Let me know if you have any questions about these results or recommendations."

                # Add device recommendations button with TurboMedics reference
                response += "\n\n**Would you like to see health monitoring devices from TurboMedics based on your results? (Yes/No)**"

                # Add a flag to indicate we're waiting for device recommendation confirmation
                st.session_state.waiting_for_device_recommendation = True
                # Set the current test type for context
                st.session_state.current_recommendation_context = "lipid_profile"

                # Add note about saved data
                # response += "\n\n*Your lipid profile data has been saved on the server. You can ask for personalized recommendations anytime.*"

            # Store lipid profile data for follow-up reminders
            st.session_state.lipid_profile_data = lipid_data
            # Set confirmation type for follow-up tracking
            st.session_state.confirmation_type = "lipid_profile"

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my lipid profile test results for analysis: {json.dumps(lipid_data, indent=2)}"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_lipid_profile = False
            st.rerun()

# Liver function test form (only shown when waiting for liver function test)
if st.session_state.waiting_for_liver_function:
    with st.form(key="liver_function_form"):
        st.markdown("### Enter Your Liver Function Test Results")
        col1, col2 = st.columns(2)

        # Helper function to create options for numeric fields
        def create_numeric_options(start, end, step=1, default=None):
            options = [f"{i:.1f}" if step < 1 else str(i) for i in np.arange(start, end + step, step)]
            options.append("N/A")
            # Set default index
            if default is None or default == "N/A":
                default_index = len(options) - 1  # N/A is the last option
            else:
                # Find the closest value to default
                closest_val = min(options[:-1], key=lambda x: abs(float(x) - float(default)))
                default_index = options.index(closest_val)
            return options, default_index

        with col1:
            # ALT (Alanine Aminotransferase) with N/A option
            alt_options, alt_default_index = create_numeric_options(0, 200, 1, "25")
            alt_selected = st.selectbox("ALT (Alanine Aminotransferase) U/L",
                                      options=alt_options,
                                      index=alt_default_index,
                                      key="liver_alt_select")
            alt = None if alt_selected == "N/A" else float(alt_selected)

            # AST (Aspartate Aminotransferase) with N/A option
            ast_options, ast_default_index = create_numeric_options(0, 200, 1, "25")
            ast_selected = st.selectbox("AST (Aspartate Aminotransferase) U/L",
                                      options=ast_options,
                                      index=ast_default_index,
                                      key="liver_ast_select")
            ast = None if ast_selected == "N/A" else float(ast_selected)

            # ALP (Alkaline Phosphatase) with N/A option
            alp_options, alp_default_index = create_numeric_options(0, 300, 1, "75")
            alp_selected = st.selectbox("ALP (Alkaline Phosphatase) U/L",
                                      options=alp_options,
                                      index=alp_default_index,
                                      key="liver_alp_select")
            alp = None if alp_selected == "N/A" else float(alp_selected)

            # Total Bilirubin with N/A option
            total_bilirubin_options, total_bilirubin_default_index = create_numeric_options(0.0, 10.0, 0.1, "1.0")
            total_bilirubin_selected = st.selectbox("Total Bilirubin mg/dL",
                                                   options=total_bilirubin_options,
                                                   index=total_bilirubin_default_index,
                                                   key="liver_total_bilirubin_select")
            total_bilirubin = None if total_bilirubin_selected == "N/A" else float(total_bilirubin_selected)

            # Direct Bilirubin with N/A option
            direct_bilirubin_options, direct_bilirubin_default_index = create_numeric_options(0.0, 5.0, 0.1, "0.3")
            direct_bilirubin_selected = st.selectbox("Direct Bilirubin mg/dL",
                                                    options=direct_bilirubin_options,
                                                    index=direct_bilirubin_default_index,
                                                    key="liver_direct_bilirubin_select")
            direct_bilirubin = None if direct_bilirubin_selected == "N/A" else float(direct_bilirubin_selected)

        with col2:
            # Albumin with N/A option
            albumin_options, albumin_default_index = create_numeric_options(1.0, 6.0, 0.1, "4.0")
            albumin_selected = st.selectbox("Albumin g/dL",
                                          options=albumin_options,
                                          index=albumin_default_index,
                                          key="liver_albumin_select")
            albumin = None if albumin_selected == "N/A" else float(albumin_selected)

            # Total Protein with N/A option
            total_protein_options, total_protein_default_index = create_numeric_options(3.0, 10.0, 0.1, "7.0")
            total_protein_selected = st.selectbox("Total Protein g/dL",
                                                 options=total_protein_options,
                                                 index=total_protein_default_index,
                                                 key="liver_total_protein_select")
            total_protein = None if total_protein_selected == "N/A" else float(total_protein_selected)

            # Age with N/A option
            age_options, age_default_index = create_numeric_options(1, 120, 1, "40")
            age_selected = st.selectbox("Age",
                                      options=age_options,
                                      index=age_default_index,
                                      key="liver_age_select")
            age = None if age_selected == "N/A" else int(age_selected)

            # Sex selection (no N/A option needed as it's already a dropdown)
            sex = st.selectbox("Sex", ["Male", "Female", "N/A"], index=0, key="liver_sex_select")

            # Alcohol consumption
            alcohol = st.selectbox("Alcohol Consumption", ["None", "Light", "Moderate", "Heavy", "N/A"], index=0, key="liver_alcohol_select")

            # Smoking status
            smoking = st.selectbox("Smoking Status", ["Non-smoker", "Former smoker", "Current smoker", "N/A"], index=0, key="liver_smoking_select")

        submit_button = st.form_submit_button(label="Analyze Liver Function")

        if submit_button:
            # Only include values that are not None (N/A selections)
            liver_data = {}
            if alt is not None:
                liver_data["ALT"] = alt
            if ast is not None:
                liver_data["AST"] = ast
            if alp is not None:
                liver_data["ALP"] = alp
            if total_bilirubin is not None:
                liver_data["Total_Bilirubin"] = total_bilirubin
            if direct_bilirubin is not None:
                liver_data["Direct_Bilirubin"] = direct_bilirubin
            if albumin is not None:
                liver_data["Albumin"] = albumin
            if total_protein is not None:
                liver_data["Total_Protein"] = total_protein
            if age is not None:
                liver_data["Age"] = age

            # Handle dropdown selections
            if sex != "N/A":
                liver_data["Sex"] = sex
            if alcohol != "N/A":
                liver_data["Alcohol_Consumption"] = alcohol
            if smoking != "N/A":
                liver_data["Smoking_Status"] = smoking

            result = submit_liver_function(liver_data)

            if "error" in result:
                response = f"Error analyzing liver function: {result['error']}"
            else:
                # Format the response
                parameter_status = result.get("parameter_status", [])
                risk_level = result.get("risk_level", "Unknown")
                confidence_level = result.get("confidence_level", "Unknown")
                recommendations = result.get("recommendations", [])

                # Create a formatted response in a friendly doctor's tone
                response = "**Liver Function Analysis**\n\n"
                response += "Hi there! I've reviewed your liver function test results, and here's what I'm seeing:\n\n"

                # Add a summary based on risk level
                if risk_level.lower() == "low":
                    response += "👍 **Good news!** Your liver function appears to be in good shape overall. "
                elif risk_level.lower() == "moderate":
                    response += "⚠️ **I'm noticing some areas of concern** in your liver function that we should keep an eye on. "
                elif risk_level.lower() == "high":
                    response += "⚠️ **I'm seeing some significant concerns** with your liver function that we should address. "
                else:
                    response += "I've analyzed your liver function test results. "

                # Add a note about confidence
                if confidence_level.lower() == "high":
                    response += "The test provided comprehensive data, so I'm quite confident in this assessment.\n\n"
                elif confidence_level.lower() == "moderate":
                    response += "The test provided most of the key measurements, giving us a reasonably good picture of your liver health.\n\n"
                elif confidence_level.lower() == "low":
                    response += "We're missing some measurements, so this is a preliminary assessment.\n\n"

                # Add parameter status in a conversational way
                if parameter_status:
                    response += "**Here's what your results tell me:**\n"
                    for status in parameter_status:
                        response += f"- {status}\n"
                    response += "\n"

                # Add recommendations in a supportive, actionable way
                if recommendations:
                    response += "**Here's what I recommend:**\n"
                    for rec in recommendations:
                        response += f"- {rec}\n"
                    response += "\n"

                # Add a supportive closing note
                if risk_level.lower() == "low":
                    response += "Keep up the good work with your liver health! Maintaining a balanced diet and limiting alcohol can help preserve your liver function."
                elif risk_level.lower() in ["moderate", "high"]:
                    response += "I'm here to help you improve your liver health. Let me know if you have any questions about these results or recommendations."
                else:
                    response += "I'm here to help you understand and improve your liver health. Feel free to ask any questions about these results."

                # Add device recommendations button
                response += "\n\n**Would you like to see recommended health devices based on your results? (Yes/No)**"

                # Add a flag to indicate we're waiting for device recommendation confirmation
                st.session_state.waiting_for_device_recommendation = True
                # Set the current test type for context
                st.session_state.current_recommendation_context = "liver_function"

            # Store liver function data for follow-up reminders
            st.session_state.liver_function_data = liver_data
            # Set confirmation type for follow-up tracking
            st.session_state.confirmation_type = "liver_function"

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my liver function test results for analysis: {json.dumps(liver_data, indent=2)}"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_liver_function = False
            st.rerun()

# Chronic Tracker form (only shown when waiting for chronic tracker)
if st.session_state.waiting_for_chronic_tracker:
    # Add a header and instructions outside the form
    st.markdown("## Chronic Condition Tracker")
    st.markdown("Track your chronic condition and get personalized feedback based on your data.")

    # Check if we have results to display
    if "chronic_tracker_results" in st.session_state and st.session_state.chronic_tracker_results:
        # Display the results
        results = st.session_state.chronic_tracker_results
        condition_type = results["condition_type"]
        result = results["result"]

        # Create a nice UI for the results
        st.markdown(f"## {condition_type.replace('_', ' ').title()} Tracking Results")

        # Add summary if available
        if "summary" in result:
            st.markdown(f"### Summary")
            st.markdown(result["summary"])

        # Add current analysis if available
        if "current_analysis" in result and result["current_analysis"]:
            st.markdown(f"### Current Status")
            current = result["current_analysis"]

            # Create columns for metrics
            cols = st.columns(len(current) if len(current) > 0 else 1)

            # Add metrics for each parameter
            i = 0
            for param, data in current.items():
                if "status" in data and "value" in data:
                    with cols[i % len(cols)]:
                        st.metric(
                            label=param.title(),
                            value=data["value"],
                            delta="Normal" if data["status"].lower() == "normal" else data["status"],
                            delta_color="normal" if data["status"].lower() == "normal" else "off"
                        )
                        if "description" in data:
                            st.caption(data["description"])
                    i += 1

        # Add trend analysis if available
        if "trend_analysis" in result and result["trend_analysis"]:
            st.markdown(f"### Trends")
            trends = result["trend_analysis"]

            # Add tracking frequency and consistency information
            if "tracking_frequency" in trends:
                tracking_frequency = trends["tracking_frequency"]
                st.markdown(f"**Tracking Frequency:** {tracking_frequency.replace('_', ' ').title()}")

                if "consistency_score" in trends:
                    consistency_score = trends["consistency_score"]
                    # Create a color based on the score
                    color = "green" if consistency_score >= 90 else "orange" if consistency_score >= 70 else "red"
                    st.markdown(f"**Consistency Score:** <span style='color:{color}'>{consistency_score}/100</span>", unsafe_allow_html=True)

                    # Add a progress bar for visual representation
                    st.progress(consistency_score/100)

                    # Add a message based on the score
                    if consistency_score >= 90:
                        st.success("Excellent tracking consistency! Keep up the good work.")
                    elif consistency_score >= 70:
                        st.info("Good tracking consistency. Try to be even more regular for better insights.")
                    else:
                        st.warning("Your tracking could be more consistent. Setting reminders might help.")

            # Add tracking period if available
            if "tracking_period" in trends:
                st.markdown(f"**Tracking Period:** {trends['tracking_period']}")

            # Display parameter-specific trends
            for param, data in trends.items():
                if isinstance(data, dict) and "direction" in data:
                    st.markdown(f"**{param.title()}:** {data['direction'].title()}")
                    if "description" in data:
                        st.markdown(data["description"])

                    # Add frequency-specific insights if available
                    if "frequency_analysis" in data and data["frequency_analysis"]:
                        analysis = data["frequency_analysis"]

                        # Add daily/weekly pattern insights
                        if "insights" in analysis and analysis["insights"]:
                            st.markdown("**Pattern Analysis:**")
                            for insight in analysis["insights"]:
                                st.markdown(f"- {insight}")

                        # Add morning/evening averages for daily tracking
                        if "morning_average" in analysis and analysis["morning_average"]:
                            st.markdown(f"**Morning Average:** {analysis['morning_average']} mg/dL")
                        if "evening_average" in analysis and analysis["evening_average"]:
                            st.markdown(f"**Evening Average:** {analysis['evening_average']} mg/dL")

                        # Add day of week averages for weekly tracking
                        if "day_averages" in analysis and analysis["day_averages"]:
                            st.markdown("**Day of Week Averages:**")
                            day_avgs = analysis["day_averages"]
                            for day, avg in day_avgs.items():
                                st.markdown(f"- {day}: {avg} mg/dL")

        # Add recommendations
        if "recommendations" in result and result["recommendations"]:
            st.markdown(f"### Recommendations")
            recommendations = result["recommendations"]

            # Filter out section headers (they start with newline)
            main_recommendations = [rec for rec in recommendations if not rec.startswith("\n")]

            # Find section indices
            tracking_section_index = -1
            continuous_care_section_index = -1

            for i, rec in enumerate(recommendations):
                if rec == "\nFor better tracking:":
                    tracking_section_index = i
                elif rec == "\nFor continuous care:":
                    continuous_care_section_index = i

            # Add main recommendations
            end_index = min(5, tracking_section_index if tracking_section_index > 0 else len(main_recommendations))
            for i, rec in enumerate(main_recommendations[:end_index], 1):
                st.markdown(f"{i}. {rec}")

            # Add tracking recommendations section if it exists
            if tracking_section_index > 0:
                st.markdown("#### For Better Tracking")
                start_idx = tracking_section_index + 1
                end_idx = continuous_care_section_index if continuous_care_section_index > 0 else len(recommendations)
                for rec in recommendations[start_idx:end_idx]:
                    if not rec.startswith("\n"):  # Skip any nested section headers
                        st.markdown(f"• {rec}")

            # Add continuous care recommendations section if it exists
            if continuous_care_section_index > 0:
                st.markdown("#### For Continuous Care")
                for rec in recommendations[continuous_care_section_index+1:]:
                    if not rec.startswith("\n"):  # Skip any nested section headers
                        st.markdown(f"• {rec}")

        # Add a button to return to chat
        if st.button("Return to Chat", key="return_to_chat_from_chronic"):
            # Get tracking frequency if available
            tracking_frequency = ""
            if "trend_analysis" in result and result["trend_analysis"] and "tracking_frequency" in result["trend_analysis"]:
                freq = result["trend_analysis"]["tracking_frequency"]
                if freq != "as_needed":
                    tracking_frequency = f"{freq} "

            # Add a summary message to chat history
            st.session_state.message_log.append({
                "role": "user",
                "content": f"I've submitted my {tracking_frequency}{condition_type.replace('_', ' ')} tracking data."
            })

            # Add a brief response to chat history
            chat_response = f"I've analyzed your {tracking_frequency}{condition_type.replace('_', ' ')} tracking data. "
            if "summary" in result:
                chat_response += result["summary"]
            else:
                chat_response += "Your data has been processed successfully."

            st.session_state.message_log.append({
                "role": "ai",
                "content": chat_response
            })

            # Clear the results and reset the form state
            st.session_state.chronic_tracker_results = None
            st.session_state.waiting_for_chronic_tracker = False

            # Rerun to update UI
            st.rerun()

    # If no results to display, show the form
    else:
        # Add a simple header with instructions
        st.markdown("Please fill out the form below to track your chronic condition.")

        # Check if form was submitted in the previous run
        if "chronic_form_submitted" in st.session_state and st.session_state.chronic_form_submitted:
            # Clear the flag
            st.session_state.chronic_form_submitted = False

            # Process the form data
            with st.spinner("Processing your data..."):
                try:
                    # Check if we have fallback results from the form submission
                    if "chronic_tracker_fallback_results" in st.session_state:
                        print("Using fallback results from form submission")
                        st.session_state.chronic_tracker_results = st.session_state.chronic_tracker_fallback_results

                        # Show success message
                        st.success("Your chronic condition data has been successfully submitted and analyzed!")

                        # Get the condition type and response from the fallback results
                        condition_type = st.session_state.chronic_tracker_fallback_results["condition_type"]
                        response = st.session_state.chronic_tracker_fallback_results["response"]

                        # Don't add to chat history - keep results in the Streamlit interface
                        # Don't reset the waiting flag - keep showing the chronic tracker interface

                        # Store the results in the chronic_tracker_results session state
                        st.session_state.chronic_tracker_results = st.session_state.chronic_tracker_fallback_results

                        # Clear the fallback results to avoid using them again
                        fallback_results = st.session_state.chronic_tracker_fallback_results
                        st.session_state.chronic_tracker_fallback_results = None

                        # Rerun to show the results in the Streamlit interface
                        st.rerun()

                    # Get the data from session state
                    condition_data = st.session_state.chronic_tracker_data
                    condition_type = condition_data.get("condition_type", "chronic condition")

                    # Get tracking frequency and measurement date from session state
                    tracking_frequency = st.session_state.get("chronic_tracking_frequency", "as_needed")
                    measurement_date_str = None
                    if "chronic_measurement_date" in st.session_state:
                        try:
                            measurement_date = st.session_state.chronic_measurement_date
                            measurement_date_str = measurement_date.isoformat() if measurement_date else None
                        except:
                            pass

                    # Submit the data with tracking frequency and measurement date
                    try:
                        result = submit_chronic_tracker(condition_data, tracking_frequency, measurement_date_str)
                        print(f"Received result from submit_chronic_tracker: {json.dumps(result, indent=2)}")
                    except Exception as e:
                        print(f"Error calling submit_chronic_tracker: {str(e)}")
                        # Generate a fallback response
                        if condition_type == "diabetes":
                            summary = "Based on your diabetes tracking data, I can see you've entered information about your glucose levels and symptoms."
                            recommendations = [
                                "Monitor your blood glucose regularly",
                                "Follow your medication regimen as prescribed",
                                "Maintain a balanced diet rich in vegetables and whole grains",
                                "Stay physically active with regular exercise",
                                "Schedule regular check-ups with your healthcare provider"
                            ]
                        elif condition_type == "hypertension":
                            summary = "Based on your hypertension tracking data, I can see you've entered information about your blood pressure readings."
                            recommendations = [
                                "Monitor your blood pressure regularly",
                                "Take medications as prescribed",
                                "Reduce sodium intake in your diet",
                                "Engage in regular physical activity",
                                "Practice stress management techniques"
                            ]
                        else:
                            summary = f"Thank you for tracking your {condition_type.replace('_', ' ')}. Regular monitoring is an important part of managing chronic conditions."
                            recommendations = [
                                "Continue tracking your symptoms and measurements",
                                "Take medications as prescribed by your healthcare provider",
                                "Maintain a healthy lifestyle with proper diet and exercise",
                                "Schedule regular check-ups with your healthcare provider",
                                "Report any significant changes in your condition promptly"
                            ]

                        result = {
                            "summary": summary,
                            "recommendations": recommendations,
                            "current_analysis": {},
                            "trend_analysis": {}
                        }

                    # Get tracking frequency for display
                    tracking_frequency_display = ""
                    if tracking_frequency != "as_needed":
                        tracking_frequency_display = f"{tracking_frequency.title()} "

                    # Format the response
                    response = f"**{tracking_frequency_display}Chronic Condition Tracking: {condition_type.replace('_', ' ').title()}**\n\n"

                    # Add summary if available
                    if "summary" in result:
                        response += result["summary"] + "\n\n"
                    else:
                        # Create a basic summary if the detailed one isn't available
                        response += f"I've analyzed your {tracking_frequency_display.lower()}tracking data. Here's what I'm seeing:\n\n"

                    # Add recommendations with sections
                    if "recommendations" in result and result["recommendations"]:
                        recommendations = result["recommendations"]
                        if recommendations:
                            # Filter out section headers (they start with newline)
                            main_recommendations = [rec for rec in recommendations if not rec.startswith("\n")]

                            # Find section indices
                            tracking_section_index = -1
                            continuous_care_section_index = -1

                            for i, rec in enumerate(recommendations):
                                if rec == "\nFor better tracking:":
                                    tracking_section_index = i
                                elif rec == "\nFor continuous care:":
                                    continuous_care_section_index = i

                            # Add main recommendations
                            response += "**Recommendations:**\n"
                            end_index = min(5, tracking_section_index if tracking_section_index > 0 else len(main_recommendations))
                            for i, rec in enumerate(main_recommendations[:end_index], 1):
                                response += f"{i}. {rec}\n"

                            # Add tracking recommendations section if it exists
                            if tracking_section_index > 0:
                                response += "\n**For Better Tracking:**\n"
                                start_idx = tracking_section_index + 1
                                end_idx = continuous_care_section_index if continuous_care_section_index > 0 else len(recommendations)
                                for rec in recommendations[start_idx:end_idx]:
                                    if not rec.startswith("\n"):  # Skip any nested section headers
                                        response += f"• {rec}\n"

                            # Add continuous care recommendations section if it exists
                            if continuous_care_section_index > 0:
                                response += "\n**For Continuous Care:**\n"
                                for rec in recommendations[continuous_care_section_index+1:]:
                                    if not rec.startswith("\n"):  # Skip any nested section headers
                                        response += f"• {rec}\n"

                    # Store the results in session state
                    st.session_state.chronic_tracker_results = {
                        "condition_type": condition_type,
                        "response": response,
                        "result": result
                    }

                    # Show success message
                    st.success("Your chronic condition data has been successfully submitted and analyzed!")

                    # Don't add to chat history - keep results in the Streamlit interface
                    # Don't reset the waiting flag - keep showing the chronic tracker interface

                    # Rerun to show the results
                    st.rerun()
                except Exception as e:
                    st.error(f"An error occurred: {str(e)}")

                    # Try to get the condition data from session state
                    try:
                        condition_data = st.session_state.chronic_tracker_data
                        condition_type = condition_data.get("condition_type", "chronic condition")

                        # Generate a fallback response
                        if condition_type == "diabetes":
                            summary = "Based on your diabetes tracking data, I can see you've entered information about your glucose levels and symptoms."
                            recommendations = [
                                "Monitor your blood glucose regularly",
                                "Follow your medication regimen as prescribed",
                                "Maintain a balanced diet rich in vegetables and whole grains",
                                "Stay physically active with regular exercise",
                                "Schedule regular check-ups with your healthcare provider"
                            ]
                        elif condition_type == "hypertension":
                            summary = "Based on your hypertension tracking data, I can see you've entered information about your blood pressure readings."
                            recommendations = [
                                "Monitor your blood pressure regularly",
                                "Take medications as prescribed",
                                "Reduce sodium intake in your diet",
                                "Engage in regular physical activity",
                                "Practice stress management techniques"
                            ]
                        else:
                            summary = f"Thank you for tracking your {condition_type.replace('_', ' ')}. Regular monitoring is an important part of managing chronic conditions."
                            recommendations = [
                                "Continue tracking your symptoms and measurements",
                                "Take medications as prescribed by your healthcare provider",
                                "Maintain a healthy lifestyle with proper diet and exercise",
                                "Schedule regular check-ups with your healthcare provider",
                                "Report any significant changes in your condition promptly"
                            ]

                        # Format the response
                        response = f"**Chronic Condition Tracking: {condition_type.replace('_', ' ').title()}**\n\n"
                        response += summary + "\n\n"
                        response += "**Recommendations:**\n"
                        for i, rec in enumerate(recommendations[:5], 1):
                            response += f"{i}. {rec}\n"

                        # Create a result object
                        result = {
                            "summary": summary,
                            "recommendations": recommendations,
                            "current_analysis": {},
                            "trend_analysis": {}
                        }

                        # Store the results in session state
                        st.session_state.chronic_tracker_results = {
                            "condition_type": condition_type,
                            "response": response,
                            "result": result
                        }

                        # Don't add to chat history - keep results in the Streamlit interface
                        # Don't reset the waiting flag - keep showing the chronic tracker interface

                        # Rerun to show the results in the Streamlit interface
                        st.rerun()
                    except Exception as inner_e:
                        print(f"Error generating fallback response: {str(inner_e)}")
                        # Don't rerun so the user can see the error

    # Use a unique key for the form that includes the condition type to avoid conflicts
    form_key = f"chronic_tracker_form_{st.session_state.chronic_condition_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    with st.form(key=form_key):
        st.markdown("### Enter Your Data")
        st.markdown("Fill in the fields below to track your chronic condition. All fields are optional - fill in what you know.")

        # Add tracking frequency and measurement date options
        col_freq, col_date = st.columns(2)

        with col_freq:
            tracking_frequency = st.selectbox(
                "Tracking Frequency",
                ["as_needed", "daily", "weekly", "monthly"],
                index=0,
                key="chronic_tracking_frequency",
                help="How often you plan to track this condition"
            )

        with col_date:
            # Default to current date
            today = datetime.now().date()
            measurement_date = st.date_input(
                "Measurement Date",
                value=today,
                key="chronic_measurement_date",
                help="When these measurements were taken"
            )
            # Convert to ISO format string
            measurement_date_str = measurement_date.isoformat() if measurement_date else None

        # Select condition type
        # Use a different key for the selectbox to avoid conflicts with session state
        condition_type = st.selectbox(
            "Select Chronic Condition",
            ["diabetes", "hypertension", "asthma", "heart_disease", "kidney_disease"],
            index=["diabetes", "hypertension", "asthma", "heart_disease", "kidney_disease"].index(st.session_state.chronic_condition_type),
            key="chronic_condition_type_select"
        )

        # We'll use the selected condition type directly without modifying session state

        # Create different form fields based on condition type
        if condition_type == "diabetes":
            col1, col2 = st.columns(2)

            with col1:
                # Helper function to create options for numeric fields
                def create_numeric_options(start, end, step=1, default=None):
                    options = [f"{i:.1f}" if step < 1 else str(i) for i in np.arange(start, end + step, step)]
                    options.append("N/A")
                    # Set default index
                    if default is None or default == "N/A":
                        default_index = len(options) - 1  # N/A is the last option
                    else:
                        # Find the closest value to default
                        closest_val = min(options[:-1], key=lambda x: abs(float(x) - float(default)))
                        default_index = options.index(closest_val)
                    return options, default_index

                # Glucose with N/A option
                glucose_options, glucose_default_index = create_numeric_options(50, 500, 1, "120")
                glucose_selected = st.selectbox("Blood Glucose (mg/dL)",
                                             options=glucose_options,
                                             index=glucose_default_index,
                                             key="chronic_glucose_select")
                glucose = None if glucose_selected == "N/A" else float(glucose_selected)

                # HbA1c with N/A option
                hba1c_options, hba1c_default_index = create_numeric_options(4.0, 14.0, 0.1, "5.7")
                hba1c_selected = st.selectbox("HbA1c (%)",
                                           options=hba1c_options,
                                           index=hba1c_default_index,
                                           key="chronic_hba1c_select")
                hba1c = None if hba1c_selected == "N/A" else float(hba1c_selected)

            with col2:
                # Medication adherence
                medication_adherence = st.selectbox(
                    "Medication Adherence",
                    ["high", "medium", "low", "N/A"],
                    index=0,
                    key="chronic_medication_adherence"
                )
                medication_adherence = None if medication_adherence == "N/A" else medication_adherence

                # Symptoms (multiselect)
                symptoms = st.multiselect(
                    "Symptoms (select all that apply)",
                    ["frequent urination", "excessive thirst", "unexplained weight loss",
                     "blurred vision", "slow-healing sores", "frequent infections",
                     "tingling hands/feet", "extreme fatigue", "none"],
                    default=["none"],
                    key="chronic_symptoms"
                )

                # Remove "none" if other symptoms are selected
                if "none" in symptoms and len(symptoms) > 1:
                    symptoms.remove("none")

                # Convert to empty list if only "none" is selected
                if symptoms == ["none"]:
                    symptoms = []

            # Prepare data for submission
            condition_data = {
                "condition_type": "diabetes",
                "glucose": glucose,
                "hba1c": hba1c,
                "medication_adherence": medication_adherence,
                "symptoms": symptoms
            }

        elif condition_type == "hypertension":
            col1, col2 = st.columns(2)

            with col1:
                # Systolic BP with N/A option
                systolic_options, systolic_default_index = create_numeric_options(80, 220, 1, "120")
                systolic_selected = st.selectbox("Systolic BP (mmHg)",
                                              options=systolic_options,
                                              index=systolic_default_index,
                                              key="chronic_systolic_select")
                systolic = None if systolic_selected == "N/A" else float(systolic_selected)

                # Diastolic BP with N/A option
                diastolic_options, diastolic_default_index = create_numeric_options(40, 140, 1, "80")
                diastolic_selected = st.selectbox("Diastolic BP (mmHg)",
                                               options=diastolic_options,
                                               index=diastolic_default_index,
                                               key="chronic_diastolic_select")
                diastolic = None if diastolic_selected == "N/A" else float(diastolic_selected)

            with col2:
                # Medication adherence
                medication_adherence = st.selectbox(
                    "Medication Adherence",
                    ["high", "medium", "low", "N/A"],
                    index=0,
                    key="chronic_bp_medication_adherence"
                )
                medication_adherence = None if medication_adherence == "N/A" else medication_adherence

                # Symptoms (multiselect)
                symptoms = st.multiselect(
                    "Symptoms (select all that apply)",
                    ["headache", "dizziness", "chest pain", "shortness of breath",
                     "nosebleeds", "visual changes", "fatigue", "none"],
                    default=["none"],
                    key="chronic_bp_symptoms"
                )

                # Remove "none" if other symptoms are selected
                if "none" in symptoms and len(symptoms) > 1:
                    symptoms.remove("none")

                # Convert to empty list if only "none" is selected
                if symptoms == ["none"]:
                    symptoms = []

            # Prepare data for submission
            condition_data = {
                "condition_type": "hypertension",
                "systolic": systolic,
                "diastolic": diastolic,
                "medication_adherence": medication_adherence,
                "symptoms": symptoms
            }

        elif condition_type == "asthma":
            col1, col2 = st.columns(2)

            with col1:
                # Peak flow with N/A option
                peak_flow_options, peak_flow_default_index = create_numeric_options(100, 800, 10, "400")
                peak_flow_selected = st.selectbox("Peak Flow (L/min)",
                                               options=peak_flow_options,
                                               index=peak_flow_default_index,
                                               key="chronic_peak_flow_select")
                peak_flow = None if peak_flow_selected == "N/A" else float(peak_flow_selected)

                # Inhaler usage with N/A option
                inhaler_options, inhaler_default_index = create_numeric_options(0, 20, 1, "2")
                inhaler_selected = st.selectbox("Rescue Inhaler Usage (times/day)",
                                             options=inhaler_options,
                                             index=inhaler_default_index,
                                             key="chronic_inhaler_select")
                inhaler_usage = None if inhaler_selected == "N/A" else float(inhaler_selected)

            with col2:
                # Medication adherence
                medication_adherence = st.selectbox(
                    "Medication Adherence",
                    ["high", "medium", "low", "N/A"],
                    index=0,
                    key="chronic_asthma_medication_adherence"
                )
                medication_adherence = None if medication_adherence == "N/A" else medication_adherence

                # Symptoms (multiselect)
                symptoms = st.multiselect(
                    "Symptoms (select all that apply)",
                    ["wheezing", "coughing", "chest tightness", "shortness of breath",
                     "nighttime awakening", "activity limitation", "none"],
                    default=["none"],
                    key="chronic_asthma_symptoms"
                )

                # Remove "none" if other symptoms are selected
                if "none" in symptoms and len(symptoms) > 1:
                    symptoms.remove("none")

                # Convert to empty list if only "none" is selected
                if symptoms == ["none"]:
                    symptoms = []

            # Prepare data for submission
            condition_data = {
                "condition_type": "asthma",
                "peak_flow": peak_flow,
                "inhaler_usage": inhaler_usage,
                "medication_adherence": medication_adherence,
                "symptoms": symptoms
            }

        elif condition_type == "heart_disease":
            col1, col2 = st.columns(2)

            with col1:
                # Chest pain frequency
                chest_pain = st.selectbox(
                    "Chest Pain Frequency",
                    ["none", "rare", "occasional", "frequent", "N/A"],
                    index=0,
                    key="chronic_chest_pain"
                )
                chest_pain = None if chest_pain == "N/A" else chest_pain

                # Shortness of breath
                shortness_of_breath = st.selectbox(
                    "Shortness of Breath",
                    ["none", "mild", "moderate", "severe", "N/A"],
                    index=0,
                    key="chronic_shortness_of_breath"
                )
                shortness_of_breath = None if shortness_of_breath == "N/A" else shortness_of_breath

                # Heart rate with N/A option
                hr_options, hr_default_index = create_numeric_options(40, 200, 1, "75")
                hr_selected = st.selectbox("Resting Heart Rate (bpm)",
                                        options=hr_options,
                                        index=hr_default_index,
                                        key="chronic_hr_select")
                heart_rate = None if hr_selected == "N/A" else float(hr_selected)

            with col2:
                # Medication adherence
                medication_adherence = st.selectbox(
                    "Medication Adherence",
                    ["high", "medium", "low", "N/A"],
                    index=0,
                    key="chronic_heart_medication_adherence"
                )
                medication_adherence = None if medication_adherence == "N/A" else medication_adherence

                # Activity level
                activity_level = st.selectbox(
                    "Activity Level",
                    ["sedentary", "light", "moderate", "active", "N/A"],
                    index=2,
                    key="chronic_activity_level"
                )
                activity_level = None if activity_level == "N/A" else activity_level

                # Symptoms (multiselect)
                symptoms = st.multiselect(
                    "Other Symptoms (select all that apply)",
                    ["fatigue", "swelling in legs/ankles", "palpitations", "dizziness",
                     "fainting", "rapid weight gain", "none"],
                    default=["none"],
                    key="chronic_heart_symptoms"
                )

                # Remove "none" if other symptoms are selected
                if "none" in symptoms and len(symptoms) > 1:
                    symptoms.remove("none")

                # Convert to empty list if only "none" is selected
                if symptoms == ["none"]:
                    symptoms = []

            # Prepare data for submission
            condition_data = {
                "condition_type": "heart_disease",
                "chest_pain": chest_pain,
                "shortness_of_breath": shortness_of_breath,
                "heart_rate": heart_rate,
                "medication_adherence": medication_adherence,
                "activity_level": activity_level,
                "symptoms": symptoms
            }

        elif condition_type == "kidney_disease":
            col1, col2 = st.columns(2)

            with col1:
                # Urine output
                urine_output = st.selectbox(
                    "Urine Output",
                    ["normal", "decreased", "increased", "N/A"],
                    index=0,
                    key="chronic_urine_output"
                )
                urine_output = None if urine_output == "N/A" else urine_output

                # Creatinine with N/A option
                creatinine_options, creatinine_default_index = create_numeric_options(0.5, 10.0, 0.1, "1.0")
                creatinine_selected = st.selectbox("Serum Creatinine (mg/dL)",
                                                options=creatinine_options,
                                                index=creatinine_default_index,
                                                key="chronic_creatinine_select")
                creatinine = None if creatinine_selected == "N/A" else float(creatinine_selected)

                # eGFR with N/A option
                egfr_options, egfr_default_index = create_numeric_options(10, 120, 1, "90")
                egfr_selected = st.selectbox("eGFR (mL/min/1.73m²)",
                                          options=egfr_options,
                                          index=egfr_default_index,
                                          key="chronic_egfr_select")
                egfr = None if egfr_selected == "N/A" else float(egfr_selected)

            with col2:
                # Medication adherence
                medication_adherence = st.selectbox(
                    "Medication Adherence",
                    ["high", "medium", "low", "N/A"],
                    index=0,
                    key="chronic_kidney_medication_adherence"
                )
                medication_adherence = None if medication_adherence == "N/A" else medication_adherence

                # Fluid intake with N/A option
                fluid_options, fluid_default_index = create_numeric_options(0, 5000, 100, "2000")
                fluid_selected = st.selectbox("Daily Fluid Intake (mL)",
                                           options=fluid_options,
                                           index=fluid_default_index,
                                           key="chronic_fluid_select")
                fluid_intake = None if fluid_selected == "N/A" else float(fluid_selected)

                # Symptoms (multiselect)
                symptoms = st.multiselect(
                    "Symptoms (select all that apply)",
                    ["fatigue", "swelling", "shortness of breath", "nausea",
                     "poor appetite", "itching", "muscle cramps", "none"],
                    default=["none"],
                    key="chronic_kidney_symptoms"
                )

                # Remove "none" if other symptoms are selected
                if "none" in symptoms and len(symptoms) > 1:
                    symptoms.remove("none")

                # Convert to empty list if only "none" is selected
                if symptoms == ["none"]:
                    symptoms = []

            # Prepare data for submission
            condition_data = {
                "condition_type": "kidney_disease",
                "urine_output": urine_output,
                "creatinine": creatinine,
                "egfr": egfr,
                "medication_adherence": medication_adherence,
                "fluid_intake": fluid_intake,
                "symptoms": symptoms
            }

        # Store the condition data in session state
        st.session_state.chronic_tracker_data = condition_data

        # Add a clear submit button with a more descriptive label and help text
        st.markdown("**Click the button below to submit your data and get personalized feedback:**")
        submit_button = st.form_submit_button(label="Submit and Track Condition", type="primary", help="Click to analyze your chronic condition data")

        # Check if the submit button was clicked
        if submit_button:
            # Set a flag in session state to indicate that the form was submitted
            st.session_state.chronic_form_submitted = True
            # Store the condition data in session state (again, to be sure)
            st.session_state.chronic_tracker_data = condition_data
            # Store the confirmation type for follow-up tracking
            st.session_state.confirmation_type = "chronic_tracker"
            # Print debug information
            print(f"Chronic tracker form submitted with data: {json.dumps(condition_data, indent=2)}")

            # Add a direct fallback in case the form processing fails
            try:
                # Process the form data directly here as a backup
                tracking_frequency = st.session_state.get("chronic_tracking_frequency", "as_needed")
                measurement_date = st.session_state.get("chronic_measurement_date", None)
                measurement_date_str = measurement_date.isoformat() if measurement_date else None

                # Generate a fallback response
                condition_type = condition_data.get("condition_type", "chronic condition")

                # Create a basic summary based on the condition type
                if condition_type == "diabetes":
                    summary = "Based on your diabetes tracking data, I can see you've entered information about your glucose levels and symptoms."
                    recommendations = [
                        "Monitor your blood glucose regularly",
                        "Follow your medication regimen as prescribed",
                        "Maintain a balanced diet rich in vegetables and whole grains",
                        "Stay physically active with regular exercise",
                        "Schedule regular check-ups with your healthcare provider"
                    ]
                elif condition_type == "hypertension":
                    summary = "Based on your hypertension tracking data, I can see you've entered information about your blood pressure readings."
                    recommendations = [
                        "Monitor your blood pressure regularly",
                        "Take medications as prescribed",
                        "Reduce sodium intake in your diet",
                        "Engage in regular physical activity",
                        "Practice stress management techniques"
                    ]
                else:
                    summary = f"Thank you for tracking your {condition_type.replace('_', ' ')}. Regular monitoring is an important part of managing chronic conditions."
                    recommendations = [
                        "Continue tracking your symptoms and measurements",
                        "Take medications as prescribed by your healthcare provider",
                        "Maintain a healthy lifestyle with proper diet and exercise",
                        "Schedule regular check-ups with your healthcare provider",
                        "Report any significant changes in your condition promptly"
                    ]

                # Store the fallback result in session state
                fallback_result = {
                    "summary": summary,
                    "recommendations": recommendations,
                    "current_analysis": {},
                    "trend_analysis": {}
                }

                # Get tracking frequency for display
                tracking_frequency_display = ""
                if tracking_frequency != "as_needed":
                    tracking_frequency_display = f"{tracking_frequency.title()} "

                # Format the response
                response = f"**{tracking_frequency_display}Chronic Condition Tracking: {condition_type.replace('_', ' ').title()}**\n\n"

                # Add summary
                response += summary + "\n\n"

                # Add recommendations
                response += "**Recommendations:**\n"
                for i, rec in enumerate(recommendations[:5], 1):
                    response += f"{i}. {rec}\n"

                # Store the results in session state as a backup
                st.session_state.chronic_tracker_fallback_results = {
                    "condition_type": condition_type,
                    "response": response,
                    "result": fallback_result
                }

                print("Stored fallback results in session state")
            except Exception as e:
                print(f"Error generating fallback response: {str(e)}")

# Mental Health Assessment form (only shown when waiting for mental health assessment)
if st.session_state.waiting_for_mental_health_assessment:
    st.markdown("### 🧠 Comprehensive Mental Health Assessment")

    step = st.session_state.mental_health_assessment_step

    # Handle Step 2 category selection outside the form
    if step == 2:
        st.markdown("**Step 2: Stress/Burnout Assessment**")
        st.markdown("Rate each statement on a scale of 1-5 (1=Never, 2=Rarely, 3=Sometimes, 4=Often, 5=Always)")

        # Multiple category selection (outside form for dynamic updates)
        categories = st.multiselect(
            "Choose one or more categories to assess:",
            ["work", "school", "relationship", "medical"],
            key="mh_stress_categories",
            default=[]
        )

        # Store selected categories in session state
        st.session_state.selected_stress_categories = categories

        if categories:
            st.info(f"Selected categories: {', '.join(categories)}")
            st.markdown("**Please complete the assessment for each selected category below:**")

    with st.form(key="mental_health_assessment_form"):
        if step != 2:  # Only show form header for non-step-2
            pass

        if step == 1:
            st.markdown("**Step 1: Basic Information**")

            col1, col2 = st.columns(2)

            with col1:
                age = st.number_input("Age", min_value=13, max_value=100, value=25, key="mh_age")
                gender = st.selectbox("Gender", ["Male", "Female", "Other"], key="mh_gender")

            with col2:
                country = st.selectbox("Country (for crisis resources)", [
                    "Argentina", "Australia", "Austria", "Bangladesh", "Belgium", "Brazil", "Canada", "China",
                    "Côte d'Ivoire", "Czech Republic", "Denmark", "Egypt", "Ethiopia", "Finland", "France",
                    "Gambia", "Germany", "Ghana", "Greece", "Hungary", "India", "Ireland", "Israel", "Italy",
                    "Kenya", "Malawi", "Malaysia", "Mauritius", "Mexico", "Netherlands", "New Zealand",
                    "Nigeria", "Norway", "Pakistan", "Poland", "Portugal", "Romania", "Russia", "Rwanda",
                    "Seychelles", "Singapore", "South Africa", "South Korea", "Spain", "Sri Lanka", "Sweden",
                    "Switzerland", "Tanzania", "Thailand", "Turkey", "Uganda", "Ukraine", "United Arab Emirates",
                    "United Kingdom", "United States"
                ], key="mh_country")
                recent_stress = st.selectbox("Have you experienced a major stressful event recently?",
                                           ["No", "Yes"], key="mh_recent_stress")

            # Store basic info
            if st.form_submit_button("Continue to Stress Assessment"):
                st.session_state.mental_health_assessment_data.update({
                    "age": age,
                    "gender": gender,
                    "country": country,
                    "recent_stress_event": recent_stress == "Yes"
                })
                st.session_state.mental_health_assessment_step = 2
                st.rerun()

        elif step == 2:
            # Get categories from session state (selected outside the form)
            categories = st.session_state.get('selected_stress_categories', [])

            # Complete stress questions from stress_depression.py (10 per category)
            stress_questions = {
                "work": [
                    "I feel overwhelmed by my job responsibilities.",
                    "I struggle to complete tasks due to fatigue or mental exhaustion.",
                    "I get fewer than 6 hours of sleep on most workdays.",
                    "I rarely take breaks or rest during the workday.",
                    "I feel emotionally detached from my work.",
                    "I feel recognized and valued at my workplace.",
                    "I work beyond 9 hours a day on a regular basis.",
                    "I experience physical symptoms such as headaches, fatigue, or insomnia due to work.",
                    "I feel like I have a healthy work-life balance.",
                    "I enjoy going to work or feel a sense of purpose in my job."
                ],
                "school": [
                    "I often feel anxious about deadlines and academic performance.",
                    "I struggle to get 7–8 hours of sleep on school nights.",
                    "I study or attend schoolwork for more than 8 hours daily.",
                    "I feel unable to cope with academic pressure.",
                    "I rarely take breaks or engage in non-academic hobbies.",
                    "I feel emotionally supported by teachers or school counselors.",
                    "I compare myself negatively to other students.",
                    "I have trouble focusing and retaining what I study.",
                    "I feel burnout from continuous academic demands.",
                    "I believe I am managing school and personal life well."
                ],
                "relationship": [
                    "I often feel emotionally drained by my relationships.",
                    "I find myself avoiding conversations with people close to me.",
                    "I feel like my needs are not being acknowledged or understood.",
                    "I frequently have conflicts or unresolved tension with loved ones.",
                    "I feel pressure to constantly give more than I receive.",
                    "I receive emotional support from those close to me.",
                    "I often feel lonely even when I am with others.",
                    "I feel stressed by trying to maintain harmony in my relationships.",
                    "I find joy and peace in my close connections.",
                    "I have space to express myself honestly and without judgment."
                ],
                "medical": [
                    "I frequently feel tired, even after resting.",
                    "My medical condition affects my mood or productivity.",
                    "I worry about my health status or future frequently.",
                    "I find it hard to manage medication or treatment schedules.",
                    "I experience sleep difficulties due to my health issues.",
                    "I feel emotionally supported by my healthcare providers.",
                    "My health limits my ability to participate in daily activities.",
                    "I feel frustrated or helpless about my health condition.",
                    "I avoid seeking help even when my symptoms worsen.",
                    "I feel in control of my health and wellness decisions."
                ]
            }

            all_responses = {}

            if categories:
                st.info(f"Selected categories: {', '.join(categories)}")

                for category in categories:
                    st.subheader(f"Assessment for: {category.capitalize()}")
                    responses = []
                    questions = stress_questions[category]

                    st.write(f"Number of questions for {category}: {len(questions)}")

                    for i, question in enumerate(questions, 1):
                        response = st.slider(
                            f"{category.capitalize()} Q{i}: {question}",
                            min_value=1,
                            max_value=5,
                            value=3,
                            key=f"stress_{category}_{i}"
                        )
                        st.caption(f"Selected: {['Never', 'Rarely', 'Sometimes', 'Often', 'Always'][response-1]}")
                        responses.append(response)

                    all_responses[category] = responses
                    st.success(f"Completed {category} assessment with {len(responses)} responses")
            else:
                st.warning("Please select at least one category to assess.")

            if st.form_submit_button("Continue to Depression Screening"):
                if categories and all_responses:
                    st.session_state.mental_health_assessment_data["stress_responses"] = all_responses
                    st.session_state.mental_health_assessment_step = 3
                    st.rerun()
                else:
                    st.error("Please select at least one category and complete the assessment.")

        elif step == 3:
            st.markdown("**Step 3: Depression Screening (PHQ-9)**")
            st.markdown("Over the last 2 weeks, how often have you been bothered by any of the following problems?")

            phq9_questions = [
                "Little interest or pleasure in doing things",
                "Feeling down, depressed, or hopeless",
                "Trouble falling/staying asleep, or sleeping too much",
                "Feeling tired or having little energy",
                "Poor appetite or overeating",
                "Feeling bad about yourself - or that you're a failure",
                "Trouble concentrating on things",
                "Moving/speaking slowly or being fidgety/restless",
                "Thoughts of self-harm or suicide"
            ]

            phq9_responses = []
            cols = st.columns(2)
            for i, question in enumerate(phq9_questions):
                with cols[i%2]:
                    response = st.selectbox(
                        f"{i+1}. {question}",
                        options=[0, 1, 2, 3],
                        format_func=lambda x: [
                            "Not at all", "Several days",
                            "More than half the days", "Nearly every day"
                        ][x],
                        key=f"phq9_q_{i}"
                    )
                    phq9_responses.append(response)

            if st.form_submit_button("Continue to Anxiety Screening"):
                st.session_state.mental_health_assessment_data["phq9_responses"] = phq9_responses
                st.session_state.mental_health_assessment_step = 4
                st.rerun()

        elif step == 4:
            st.markdown("**Step 4: Anxiety Screening (GAD-7)**")
            st.markdown("Over the last 2 weeks, how often have you been bothered by the following problems?")

            gad7_questions = [
                "Feeling nervous, anxious, or on edge",
                "Not being able to stop worrying",
                "Worrying too much about different things",
                "Trouble relaxing",
                "Being so restless that it's hard to sit still",
                "Becoming easily annoyed or irritable",
                "Feeling afraid as if something awful might happen"
            ]

            gad7_responses = []
            cols = st.columns(2)
            for i, question in enumerate(gad7_questions):
                with cols[i%2]:
                    response = st.selectbox(
                        f"{i+1}. {question}",
                        options=[0, 1, 2, 3],
                        format_func=lambda x: [
                            "Not at all", "Several days",
                            "More than half the days", "Nearly every day"
                        ][x],
                        key=f"gad7_q_{i}"
                    )
                    gad7_responses.append(response)

            if st.form_submit_button("Complete Assessment"):
                st.session_state.mental_health_assessment_data["gad7_responses"] = gad7_responses

                # Submit the complete assessment
                result = submit_mental_health_assessment(st.session_state.mental_health_assessment_data)

                if "error" in result:
                    response = f"I'm sorry, but there was an error with your mental health assessment: {result['error']}\n\n"
                    response += "Please try again later or consult with a healthcare professional if you're concerned about your mental health."
                else:
                    # Create a doctor-like summary
                    assessments = result.get("assessments", {})
                    risk_prediction = assessments.get("ml_risk_prediction", {})
                    risk_level = risk_prediction.get("risk_level", "Unknown")
                    confidence = risk_prediction.get("confidence", 0)

                    # Start with a friendly, doctor-like introduction
                    response = "I've completed your comprehensive mental health assessment. Here's what I found:\n\n"

                    # Add risk level with appropriate emoji and tone
                    if risk_level == "High Risk":
                        response += f"**Overall Mental Health Risk: 🚨 {risk_level}** (Confidence: {confidence:.1f}%)\n\n"
                        response += "Based on your responses, I'm concerned about your current mental health status. "
                    elif risk_level == "Moderate Risk":
                        response += f"**Overall Mental Health Risk: ⚠️ {risk_level}** (Confidence: {confidence:.1f}%)\n\n"
                        response += "Your assessment shows some areas that need attention. "
                    else:
                        response += f"**Overall Mental Health Risk: ✅ {risk_level}** (Confidence: {confidence:.1f}%)\n\n"
                        response += "Your mental health assessment shows positive indicators. "

                    # Add detailed assessment results
                    if "summary" in result:
                        response += f"{result['summary']}\n\n"

                    # Add specific scores in a doctor-like format
                    depression_score = assessments.get("depression_phq9", {})
                    anxiety_score = assessments.get("anxiety_gad7", {})
                    stress_scores = assessments.get("stress_burnout", {})

                    if depression_score or anxiety_score or stress_scores:
                        response += "**Assessment Breakdown:**\n"

                        if depression_score:
                            dep_total = depression_score.get("total_score", 0)
                            dep_severity = depression_score.get("severity", "Unknown")
                            response += f"• Depression (PHQ-9): {dep_total}/27 - {dep_severity}\n"

                        if anxiety_score:
                            anx_total = anxiety_score.get("total_score", 0)
                            anx_severity = anxiety_score.get("severity", "Unknown")
                            response += f"• Anxiety (GAD-7): {anx_total}/21 - {anx_severity}\n"

                        if stress_scores:
                            for category, scores in stress_scores.items():
                                if isinstance(scores, dict):
                                    stress_total = scores.get("total_score", 0)
                                    stress_max = scores.get("max_score", 50)
                                    stress_interp = scores.get("interpretation", "Unknown")
                                    response += f"• {category.capitalize()} Stress: {stress_total}/{stress_max} - {stress_interp}\n"
                        response += "\n"

                    # Add recommendations in a more personal, doctor-like tone
                    if "recommendations" in result and result["recommendations"]:
                        response += "**My Recommendations for You:**\n"
                        for rec in result["recommendations"]:
                            # Make recommendations sound more personal and doctor-like
                            if not rec.startswith("•") and not rec.startswith("-"):
                                response += f"• {rec}\n"
                            else:
                                response += f"{rec}\n"
                        response += "\n"

                    # Add follow-up reminders with a caring tone
                    if "follow_up_reminders" in result and result["follow_up_reminders"]:
                        response += "**Important Follow-up Steps:**\n"
                        for reminder in result["follow_up_reminders"]:
                            if not reminder.startswith("•") and not reminder.startswith("-"):
                                response += f"• {reminder}\n"
                            else:
                                response += f"{reminder}\n"
                        response += "\n"

                    # Add crisis resources with appropriate urgency
                    if "crisis_resources" in result and result["crisis_resources"]:
                        crisis = result["crisis_resources"]
                        if risk_level == "High Risk":
                            response += "**🚨 Immediate Support Resources:**\n"
                            response += "Given your assessment results, please know that help is available 24/7:\n\n"
                        else:
                            response += "**Support Resources Available:**\n"

                        if "resources" in crisis:
                            for resource in crisis["resources"]:
                                response += f"• {resource}\n"
                        response += "\n"

                    # Add device recommendation prompt (like other health tests)
                    response += "**Would you like to see mental health monitoring devices from TurboMedics based on your assessment? (Yes/No)**"

                    # Set up for device recommendation flow
                    st.session_state.waiting_for_device_recommendation = True
                    st.session_state.current_recommendation_context = "mental_health_assessment"

                    # Add final caring note
                    response += "\n\n**Remember:** Your mental health matters, and seeking support is a sign of strength. This assessment provides insights, but please consult with a mental health professional for personalized care and treatment options."

                # Save results and add to chat
                st.session_state.mental_health_assessment_results = result
                st.session_state.message_log.append({"role": "user", "content": "I've completed the comprehensive mental health assessment."})
                st.session_state.message_log.append({"role": "ai", "content": response})

                # Reset form state
                st.session_state.waiting_for_mental_health_assessment = False
                st.session_state.mental_health_assessment_step = 1
                st.session_state.mental_health_assessment_data = {}

                st.rerun()

# Weekly Digest form (only shown when waiting for weekly digest)
if st.session_state.waiting_for_weekly_digest:
    with st.form(key="weekly_digest_form"):
        st.markdown("### Weekly Health Digest")
        st.markdown("Enter your current vital signs to generate a weekly health digest with insights and recommendations.")

        col1, col2 = st.columns(2)

        with col1:
            # Blood Pressure (Systolic)
            bp_systolic = st.number_input("Blood Pressure (Systolic)", min_value=70, max_value=200, value=120, key="digest_bp_sys")

            # Blood Pressure (Diastolic)
            bp_diastolic = st.number_input("Blood Pressure (Diastolic)", min_value=40, max_value=120, value=80, key="digest_bp_dia")

            # Heart Rate
            heart_rate = st.number_input("Heart Rate (bpm)", min_value=40, max_value=200, value=75, key="digest_hr")

            # Weight (for BMI calculation)
            weight = st.number_input("Weight (kg)", min_value=30.0, max_value=200.0, value=70.0, step=0.1, key="digest_weight")

        with col2:
            # Glucose
            glucose = st.number_input("Glucose (mg/dL)", min_value=50, max_value=300, value=100, key="digest_glucose")

            # SpO2
            spo2 = st.number_input("SpO2 (%)", min_value=80, max_value=100, value=98, key="digest_spo2")

            # Temperature
            temperature = st.number_input("Temperature (°C)", min_value=35.0, max_value=42.0, value=36.8, step=0.1, key="digest_temp")

            # Height (for BMI calculation)
            height = st.number_input("Height (cm)", min_value=100.0, max_value=220.0, value=170.0, step=0.1, key="digest_height")

        # Calculate BMI
        bmi = weight / ((height / 100) ** 2) if height > 0 else 0
        st.info(f"Calculated BMI: {bmi:.1f}")

        submit_button = st.form_submit_button(label="Generate Weekly Digest")

        if submit_button:
            # Combine all vital signs data
            vital_signs = {
                "Blood Pressure (Systolic)": bp_systolic,
                "Blood Pressure (Diastolic)": bp_diastolic,
                "ECG (Heart Rate)": heart_rate,
                "Weight (BMI)": bmi,
                "Glucose": glucose,
                "SpO2": spo2,
                "Temperature": temperature
            }

            # Submit data for weekly digest
            result = get_weekly_digest(vital_signs)

            if "error" in result:
                response = f"Error generating weekly digest: {result['error']}"
            else:
                # Format the response
                response = "**Weekly Health Digest**\n\n"

                if "weekly_digest" in result:
                    digest = result["weekly_digest"]

                    if "summary" in digest:
                        response += f"**Summary:** {digest['summary']}\n\n"

                    if "insights" in digest:
                        response += "**Weekly Insights:**\n"
                        for insight in digest["insights"]:
                            response += f"- {insight}\n"
                        response += "\n"

                    if "recommendations" in digest:
                        response += "**Personalized Recommendations:**\n"
                        for rec in digest["recommendations"]:
                            response += f"- {rec}\n"
                        response += "\n"

                    if "health_score" in digest:
                        score = digest["health_score"]
                        response += f"**Weekly Health Score:** {score}/100\n\n"

                    if "next_steps" in digest:
                        response += "**Suggested Next Steps:**\n"
                        for step in digest["next_steps"]:
                            response += f"- {step}\n"

            # Save the result for display
            st.session_state.weekly_digest_results = result

            # Add to chat history
            st.session_state.message_log.append({"role": "user", "content": "I've submitted my vital signs for a weekly health digest."})
            st.session_state.message_log.append({"role": "ai", "content": response})

            # Reset the form state
            st.session_state.waiting_for_weekly_digest = False

            # Rerun to update UI
            st.rerun()

# Chat input
user_query = st.chat_input("Type your message here...")

# Process user input
if user_query:
    # Add user message to chat history
    st.session_state.message_log.append({"role": "user", "content": user_query})

    # Check if waiting for confirmation
    if st.session_state.waiting_for_confirmation and user_query.lower() == "yes":
        response = handle_confirmation(st.session_state.confirmation_type)
        st.session_state.message_log.append({"role": "ai", "content": response})
    # Check if waiting for device recommendation confirmation
    elif st.session_state.waiting_for_device_recommendation:
        if user_query.lower() == "yes" or "yes" in user_query.lower():
            # Request device recommendations directly from the server
            response = request_device_recommendations()
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_device_recommendation = False
        elif user_query.lower() == "no" or "no" in user_query.lower():
            # User declined TurboMedics device recommendations, offer follow-up reminders instead
            response = "No problem. You can always visit [TurboMedics](https://www.turbomedics.com/products) later if you need health monitoring devices. Would you like to see follow-up reminders based on your test results instead? (Yes/No)"
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_device_recommendation = False
            st.session_state.waiting_for_followup_confirmation = True
            # Keep the current test context for follow-up reminders
            st.session_state.followup_context = st.session_state.current_recommendation_context
        else:
            # User provided an unclear response
            response = "I'm not sure if you want to see device recommendations. Please answer with 'Yes' or 'No'."
            st.session_state.message_log.append({"role": "ai", "content": response})
            # Keep waiting for a clear response
            st.session_state.waiting_for_device_recommendation = True

    # Check if waiting for follow-up reminder confirmation
    elif st.session_state.waiting_for_followup_confirmation:
        if user_query.lower() == "yes" or "yes" in user_query.lower():
            # Generate follow-up reminders based on the test context
            response = request_followup_reminder()
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_followup_confirmation = False
        elif user_query.lower() == "no" or "no" in user_query.lower():
            # User declined follow-up reminders
            response = "No problem. Is there anything else I can help you with regarding your health data or test results?"
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_followup_confirmation = False
        else:
            # User provided an unclear response
            response = "I'm not sure if you want to see follow-up reminders. Please answer with 'Yes' or 'No'."
            st.session_state.message_log.append({"role": "ai", "content": response})
            # Keep waiting for a clear response
            st.session_state.waiting_for_followup_confirmation = True

    # Check if waiting for symptom checker input
    elif st.session_state.waiting_for_symptom_checker:
        if st.session_state.symptom_checker_step == 1:
            # Save the symptoms
            symptoms = user_query

            # Move to step 2 to collect additional information
            st.session_state.symptom_checker_step = 2

            # Ask for additional information
            response = "Thank you for describing your symptoms. To provide a more accurate analysis, please answer a few additional questions:\n\n"
            response += "1. How long have you been experiencing these symptoms? (e.g., hours, days, weeks)\n"
            response += "2. How would you rate the severity of your symptoms on a scale from 1-10?\n"
            response += "3. What is your age? (This helps with age-specific analysis)\n"
            response += "4. What is your biological sex? (This helps with sex-specific analysis)\n\n"
            response += "You can answer these questions in any order or format. If you prefer not to answer any question, just type 'skip' and we'll proceed with the analysis."

            st.session_state.message_log.append({"role": "ai", "content": response})

            # Store the symptoms in session state
            st.session_state.symptom_text = symptoms

        elif st.session_state.symptom_checker_step == 2:
            # Extract additional information from the user's response
            additional_info = user_query.lower()

            # Initialize variables with default values
            age = None
            sex = None
            duration = "unknown"
            severity = "moderate"

            # Extract age
            age_match = re.search(r'age[:\s]*(\d+)', additional_info)
            if age_match:
                try:
                    age = int(age_match.group(1))
                except:
                    pass

            # Extract sex
            if "male" in additional_info:
                sex = "male"
            elif "female" in additional_info:
                sex = "female"

            # Extract duration
            if "hour" in additional_info:
                duration = "hours"
            elif "day" in additional_info:
                duration = "days"
            elif "week" in additional_info:
                duration = "weeks"
            elif "month" in additional_info:
                duration = "months"

            # Extract severity
            severity_match = re.search(r'severity[:\s]*(\d+)|(\d+)[\s/]*10', additional_info)
            if severity_match:
                severity_value = severity_match.group(1) or severity_match.group(2)
                try:
                    severity_num = int(severity_value)
                    if severity_num <= 3:
                        severity = "low"
                    elif severity_num <= 7:
                        severity = "moderate"
                    else:
                        severity = "high"
                except:
                    pass

            # Submit the symptoms and additional information to the server
            result = submit_symptoms(
                st.session_state.symptom_text,
                age=age,
                sex=sex,
                duration=duration,
                severity=severity
            )

            # Check for errors
            if "error" in result:
                response = f"I'm sorry, but there was an error analyzing your symptoms: {result['error']}\n\n"
                response += "Please try again later or consult with a healthcare professional if you're concerned about your symptoms."
            else:
                # Format the response
                response = "Based on the symptoms you've described, here's my assessment:\n\n"

                # Check for emergency level
                if result.get("urgency_level") == "emergency":
                    response += "🚨 **URGENT MEDICAL ATTENTION NEEDED** 🚨\n\n"
                    response += f"{result.get('message', 'Please seek immediate medical care.')}\n\n"
                    response += f"{result.get('recommendation', '')}\n\n"
                else:
                    # Add potential conditions
                    potential_conditions = result.get("potential_conditions", [])
                    if potential_conditions:
                        response += "**Potential conditions to discuss with your healthcare provider:**\n"
                        for condition in potential_conditions:
                            response += f"- {condition}\n"
                        response += "\n"

                    # Add urgency level with appropriate emoji
                    urgency_level = result.get("urgency_level", "low")
                    if urgency_level == "high":
                        response += "**Urgency Level: 🚨 High** - Please consult with a healthcare provider soon.\n\n"
                    elif urgency_level == "moderate":
                        response += "**Urgency Level: ⚠️ Moderate** - Consider scheduling an appointment with your healthcare provider.\n\n"
                    else:
                        response += "**Urgency Level: ✅ Low** - Monitor your symptoms and consult a healthcare provider if they worsen.\n\n"

                    # Add recommended tests
                    recommended_tests = result.get("recommended_tests", [])
                    if recommended_tests:
                        response += "**Tests your healthcare provider might consider:**\n"
                        for test in recommended_tests:
                            response += f"- {test}\n"
                        response += "\n"

                    # Add home care recommendations
                    home_care = result.get("home_care_recommendations", [])
                    if home_care:
                        response += "**Self-care recommendations:**\n"
                        for care in home_care:
                            response += f"- {care}\n"
                        response += "\n"

                    # Add disclaimer
                    response += "**Important:** This is not a medical diagnosis. The information provided is for educational purposes only and should not replace professional medical advice. Please consult with a healthcare provider for proper evaluation and treatment."

            # Add the response to the chat history
            st.session_state.message_log.append({"role": "ai", "content": response})

            # Reset the symptom checker state
            st.session_state.waiting_for_symptom_checker = False
            st.session_state.symptom_checker_step = 1
            if "symptom_text" in st.session_state:
                del st.session_state.symptom_text

    # Check if waiting for lab test explainer input
    elif st.session_state.waiting_for_lab_test_explainer:
        # Get the test name from the user query
        test_name = user_query.strip()

        # Request lab test explanation from the server
        result = request_lab_test_explanation(test_name=test_name)

        # Check for errors
        if "error" in result:
            response = f"I'm sorry, but there was an error retrieving information about the lab test: {result['error']}\n\n"
            response += "Please try again with a different test name."
        else:
            # Format the response
            response = f"**{test_name} Explained**\n\n"

            # Add note if this was a fuzzy match
            if "note" in result:
                response += f"*{result['note']}*\n\n"

            # Add description
            if "description" in result:
                response += f"{result['description']}\n\n"

            # Add what it measures
            if "what_it_measures" in result:
                response += "**What it measures:**\n"
                for item in result["what_it_measures"]:
                    response += f"- {item}\n"
                response += "\n"

            # Add normal ranges
            if "normal_ranges" in result and result["normal_ranges"]:
                response += "**Normal ranges:**\n"
                for param, range_val in result["normal_ranges"].items():
                    response += f"- {param}: {range_val}\n"
                response += "\n"

            # Add when it's ordered
            if "when_ordered" in result and result["when_ordered"]:
                response += "**When this test is ordered:**\n"
                for item in result["when_ordered"][:3]:  # Limit to 3 items
                    response += f"- {item}\n"
                response += "\n"

            # Add patient education
            if "patient_education" in result and result["patient_education"]:
                response += f"**Patient education:**\n{result['patient_education']}\n\n"

            # Add preparation
            if "preparation" in result and result["preparation"]:
                response += f"**Preparation:**\n{result['preparation']}\n\n"

            # Add interpretation
            if "interpretation" in result and result["interpretation"]:
                response += f"**Interpretation:**\n{result['interpretation']}"

            # Add a note about other tests
            response += "\n\n**Would you like to learn about another lab test?** Just type the name of any lab test you're curious about."

            # Add a note about entering test values
            response += "\n\nIf you have your own test results for this test, you can use the Lab Test Explainer quick action button above to enter your values for personalized analysis."

        # Add the response to the chat history
        st.session_state.message_log.append({"role": "ai", "content": response})
    # Check if user is directly asking for health consultation
    elif any(keyword in user_query.lower() for keyword in ["health consultation", "consult", "medical advice", "talk to doctor", "professional opinion",
                                                          "medical consultation", "doctor advice", "clinical advice", "appointment", "schedule",
                                                          "checkup", "follow-up", "follow up", "doctor visit", "specialist", "when should i see a doctor",
                                                          "care plan", "care coordination", "medical appointment", "screening", "test schedule",
                                                          "recommended tests", "proactive care", "preventive care", "preventative care", "health plan"]):
        # Request health consultation directly from the server
        response = request_health_consultation()

        # Check if the response contains a message about insufficient data
        if "insufficient data" in response.lower():
            # Add a more helpful message suggesting what data to enter
            response += "\n\nTo get a more comprehensive health consultation, please consider entering your data using the quick action buttons above:\n\n"
            response += "- **Health Score**: For a comprehensive health assessment\n"
            response += "- **Monitor Vitals**: To track your basic health metrics\n"
            response += "- **Kidney Function**: For renal health assessment\n"
            response += "- **Cardiac Health**: For cardiovascular risk assessment\n"
            response += "- **Lung Capacity**: For respiratory health evaluation\n"

        st.session_state.message_log.append({"role": "ai", "content": response})
    # Check if user is asking for recommendations - let the server handle this
    elif any(keyword in user_query.lower() for keyword in ["recommendations", "what should i improve", "how can i improve", "what needs improvement", "vitals that need improvement", "advice", "suggest", "tips", "what should i do"]):
        # Let the server generate personalized recommendations based on saved health data
        response = query_agent(user_query, selected_model)
        st.session_state.message_log.append({"role": "ai", "content": response})
    else:
        # Get response from agent - the server will now handle including health data in responses
        response = query_agent(user_query, selected_model)
        st.session_state.message_log.append({"role": "ai", "content": response})

    # Rerun to update UI
    st.rerun()

# Lifestyle Coach form (only shown when waiting for lifestyle tracking)
if st.session_state.waiting_for_lifestyle_tracking:
    with st.form(key="lifestyle_tracking_form"):
        st.markdown("### Lifestyle Coach")
        st.markdown("Track your lifestyle habits for personalized coaching and recommendations.")

        # Date selection
        tracking_date = st.date_input("Date", datetime.now(), key="lifestyle_tracking_date")

        # Create tabs for different categories
        physical_tab, nutrition_tab, mental_tab, sleep_tab = st.tabs(["Physical Activity", "Nutrition", "Mental Wellbeing", "Sleep"])

        with physical_tab:
            # Exercise duration
            exercise_duration = st.number_input("Exercise Duration (minutes)", min_value=0, max_value=300, value=30, key="lifestyle_exercise_duration")

            # Exercise type
            exercise_type = st.selectbox("Exercise Type", ["Walking", "Running", "Cycling", "Swimming", "Strength Training", "Yoga", "Other"], index=0, key="lifestyle_exercise_type")

            # Steps
            steps = st.number_input("Steps", min_value=0, max_value=50000, value=5000, key="lifestyle_steps")

        with nutrition_tab:
            # Water intake
            water_intake = st.number_input("Water Intake (cups)", min_value=0, max_value=20, value=8, key="lifestyle_water")

            # Fruit and vegetable servings
            fruit_veg = st.number_input("Fruit & Vegetable Servings", min_value=0, max_value=20, value=5, key="lifestyle_fruit_veg")

            # Processed food servings
            processed_food = st.number_input("Processed Food Servings", min_value=0, max_value=20, value=2, key="lifestyle_processed_food")

        with mental_tab:
            # Stress level
            stress_level = st.slider("Stress Level", min_value=1, max_value=10, value=4, key="lifestyle_stress")

            # Mindfulness practice
            mindfulness = st.number_input("Mindfulness Practice (minutes)", min_value=0, max_value=180, value=10, key="lifestyle_mindfulness")

            # Mood
            mood = st.selectbox("Overall Mood", ["Excellent", "Good", "Fair", "Poor"], index=1, key="lifestyle_mood")

        with sleep_tab:
            # Sleep duration
            sleep_duration = st.number_input("Sleep Duration (hours)", min_value=0.0, max_value=24.0, value=7.5, step=0.5, key="lifestyle_sleep_duration")

            # Sleep quality
            sleep_quality = st.selectbox("Sleep Quality", ["Excellent", "Good", "Fair", "Poor"], index=1, key="lifestyle_sleep_quality")

            # Bedtime consistency
            bedtime_consistent = st.selectbox("Consistent Bedtime", ["Yes", "No"], index=0, key="lifestyle_bedtime_consistent")

        submit_button = st.form_submit_button(label="Get Personalized Coaching")

        if submit_button:
            # Combine all lifestyle data
            habits_data = {
                "date": tracking_date.strftime("%Y-%m-%d"),
                "physical_activity": {
                    "exercise_duration": exercise_duration,
                    "exercise_type": exercise_type,
                    "steps": steps
                },
                "nutrition": {
                    "water_intake": water_intake,
                    "fruit_veg_servings": fruit_veg,
                    "processed_food_servings": processed_food
                },
                "mental_wellbeing": {
                    "stress_level": stress_level,
                    "mindfulness_minutes": mindfulness,
                    "mood": mood
                },
                "sleep": {
                    "duration": sleep_duration,
                    "quality": sleep_quality,
                    "consistent_bedtime": bedtime_consistent == "Yes"
                }
            }

            # Save the data to session state
            st.session_state.lifestyle_habits_data = habits_data

            # Submit data for lifestyle tracking
            result = track_lifestyle_habits(habits_data)

            if "error" in result:
                response = f"Error generating lifestyle coaching: {result['error']}"
            else:
                # Format the response
                response = "**Lifestyle Coaching Analysis**\n\n"

                if "weekly_summary" in result:
                    response += "**Weekly Summary:**\n"
                    for category, summary in result["weekly_summary"].items():
                        response += f"*{category}*: {summary}\n"
                    response += "\n"

                if "recommendations" in result:
                    response += "**Personalized Coaching Recommendations:**\n"
                    for rec in result["recommendations"]:
                        response += f"- {rec}\n"

            st.session_state.message_log.append({"role": "user", "content": "I've submitted my lifestyle information for personalized coaching."})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_lifestyle_tracking = False
            st.rerun()

# Chronic Risk Prediction form (only shown when waiting for chronic risk prediction)
if st.session_state.waiting_for_chronic_risk:
    with st.form(key="chronic_risk_form"):
        st.markdown("### Chronic Disease Risk Prediction")
        st.markdown("Enter your information to predict your risk of developing chronic diseases.")

        col1, col2 = st.columns(2)

        with col1:
            # Age
            age = st.number_input("Age", min_value=18, max_value=120, value=40, key="chronic_risk_age")

            # Sex
            sex = st.selectbox("Sex", ["Male", "Female"], index=0, key="chronic_risk_sex")

            # BMI
            bmi = st.number_input("BMI", min_value=10.0, max_value=50.0, value=24.5, step=0.1, key="chronic_risk_bmi")

            # Blood Pressure (Systolic)
            bp_systolic = st.number_input("Blood Pressure (Systolic)", min_value=70, max_value=200, value=120, key="chronic_risk_bp_sys")

            # Blood Pressure (Diastolic)
            bp_diastolic = st.number_input("Blood Pressure (Diastolic)", min_value=40, max_value=120, value=80, key="chronic_risk_bp_dia")

        with col2:
            # Glucose
            glucose = st.number_input("Glucose (mg/dL)", min_value=50, max_value=300, value=100, key="chronic_risk_glucose")

            # Smoking Status
            smoking = st.selectbox("Smoking Status", ["Never", "Former", "Current"], index=0, key="chronic_risk_smoking")

            # Physical Activity
            physical_activity = st.selectbox("Physical Activity Level", ["Low", "Moderate", "High"], index=1, key="chronic_risk_activity")

            # Family History
            family_history = st.selectbox("Family History of Chronic Disease", ["Yes", "No"], index=1, key="chronic_risk_family")

            # Alcohol Consumption
            alcohol = st.number_input("Alcohol Consumption (drinks/week)", min_value=0, max_value=50, value=2, key="chronic_risk_alcohol")

        submit_button = st.form_submit_button(label="Predict Risk")

        if submit_button:
            # Combine all chronic risk data
            chronic_data = {
                "Age": age,
                "Sex": sex,
                "BMI": bmi,
                "Blood_Pressure_Systolic": bp_systolic,
                "Blood_Pressure_Diastolic": bp_diastolic,
                "Glucose": glucose,
                "Smoking_Status": smoking,
                "Physical_Activity": physical_activity,
                "Family_History": family_history,
                "Alcohol_Consumption": alcohol
            }

            # Save the data to session state
            st.session_state.chronic_risk_data = chronic_data

            # Submit data for chronic risk prediction
            result = request_chronic_risk_prediction(chronic_data)

            if "error" in result:
                response = f"Error predicting chronic disease risk: {result['error']}"
            else:
                # Format the response
                response = "**Chronic Disease Risk Prediction**\n\n"

                if "risk_scores" in result:
                    response += "**Risk Scores:**\n"
                    for disease, score in result["risk_scores"].items():
                        risk_level = "Low" if score < 0.2 else "Moderate" if score < 0.5 else "High"
                        emoji = "🟢" if score < 0.2 else "🟡" if score < 0.5 else "🔴"
                        response += f"- {disease}: {emoji} {risk_level} ({score:.2f})\n"
                    response += "\n"

                if "recommendations" in result:
                    response += "**Personalized Recommendations:**\n"
                    for rec in result["recommendations"]:
                        response += f"- {rec}\n"
                    response += "\n"

                if "follow_up" in result:
                    response += "**Recommended Follow-up:**\n"
                    for follow in result["follow_up"]:
                        response += f"- {follow}\n"

            st.session_state.message_log.append({"role": "user", "content": "I've submitted my information for chronic disease risk prediction."})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_chronic_risk = False
            st.rerun()

# Document Summarizer form (only shown when waiting for document summarization)
if st.session_state.waiting_for_doc_summarizer:
    with st.form(key="doc_summarizer_form"):
        st.markdown("### Medical Document Summarizer")
        st.markdown("Upload a medical document (PDF format) for AI summarization.")

        # File uploader for PDF documents
        uploaded_file = st.file_uploader("Upload PDF Document", type=["pdf"], key="doc_summarizer_file")

        # Model selection
        model = st.selectbox(
            "Select Model for Summarization",
            MODELS,
            index=MODELS.index(DEFAULT_MODEL) if DEFAULT_MODEL in MODELS else 0,
            key="doc_summarizer_model"
        )

        submit_button = st.form_submit_button(label="Summarize Document")

        if submit_button:
            if uploaded_file is not None:
                # Get the file data
                file_data = uploaded_file.getvalue()
                file_name = uploaded_file.name

                # Submit the document for summarization
                result = summarize_medical_document(file_data, model=model)

                if "error" in result:
                    response = f"Error summarizing document: {result['error']}"
                else:
                    # Format the response
                    response = "**Medical Document Summary**\n\n"

                    if "summary" in result:
                        response += f"{result['summary']}\n\n"

                    if "length" in result:
                        response += f"*Document length: {result['length']} characters*\n\n"

                    response += f"*Document: {file_name}*"

                st.session_state.message_log.append({"role": "user", "content": f"I've uploaded a medical document: {file_name}"})
                st.session_state.message_log.append({"role": "ai", "content": response})
                st.session_state.waiting_for_doc_summarizer = False
                st.rerun()
            else:
                st.error("Please upload a PDF document.")

# Footer
st.markdown("---")
st.caption(f"© {datetime.now().year} Dr. Deuce Health Assistant | Last updated: {datetime.now().strftime('%Y-%m-%d')}")
